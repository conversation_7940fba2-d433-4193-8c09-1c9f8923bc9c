21:08:13.869/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/看广告.js]
21:08:13.890/D: 🔍 开始看广告检测...
21:08:14.258/D: ⏰ 等待5秒后开始检测...
21:08:19.268/D: 🚀 开始执行检测...
21:08:19.269/D: 📸 正在捕获屏幕画面...
21:08:19.270/D: ✅ 屏幕截图成功
21:08:19.302/D: 📋 检测区域: 右广告
21:08:19.305/D:   🔍 快速分析背景颜色...
21:08:19.336/D:   🎯 开始白色过滤处理（包含前景轮廓保护）...
21:08:19.337/D:   📊 输入图像信息: 4通道, 尺寸: 110x176
21:08:19.337/D:   📝 处理彩色图像...
21:08:19.346/D:   🛡️ 创建前景轮廓保护掩码...
21:08:19.347/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.352/D:       📊 前景色保护像素数量：0
21:08:19.352/D:   📊 检测到前景轮廓像素：0 个
21:08:19.525/D:   💾 白色过滤处理图已保存: /storage/emulated/0/Pictures/右广告_白色过滤_1752325699362_处理图.jpg
21:08:19.526/D: 🔍 使用原始区域图像进行初步轮廓分析...
21:08:19.528/D:   ⚠️ 重要：使用原始图像避免白色过滤导致轮廓丢失
21:08:19.532/D:   📝 使用轮廓保护模式
21:08:19.537/D:   🔍 使用低阈值Canny边缘检测，适应暗色轮廓...
21:08:19.543/D:   🔍 开始检测边缘图中的轮廓坐标...
21:08:19.548/D:   📊 边缘检测发现 25 个轮廓
21:08:19.549/D:   📍 边缘检测全屏轮廓坐标信息:
21:08:19.550/D:     - 边缘轮廓1: (477, 175) 尺寸: 104×6 面积: 2 [区域内坐标: (52, 173)]
21:08:19.551/D:     - 边缘轮廓2: (531, 172) 尺寸: 8×12 面积: 7 [区域内坐标: (106, 170)]
21:08:19.552/D:     - 边缘轮廓3: (535, 165) 尺寸: 1×1 面积: 0 [区域内坐标: (110, 163)]
21:08:19.552/D:     - 边缘轮廓4: (478, 165) 尺寸: 106×8 面积: 6 [区域内坐标: (53, 163)]
21:08:19.553/D:     - 边缘轮廓5: (472, 137) 尺寸: 93×11 面积: 8 [区域内坐标: (47, 135)]
21:08:19.554/D:     - 边缘轮廓6: (488, 103) 尺寸: 1×1 面积: 0 [区域内坐标: (63, 101)]
21:08:19.555/D:     - 边缘轮廓7: (494, 105) 尺寸: 18×22 面积: 94 [区域内坐标: (69, 103)]
21:08:19.555/D:     - 边缘轮廓8: (476, 105) 尺寸: 17×22 面积: 127 [区域内坐标: (51, 103)]
21:08:19.556/D:     - 边缘轮廓9: (455, 105) 尺寸: 18×22 面积: 309 [区域内坐标: (30, 103)]
21:08:19.557/D:     - 边缘轮廓10: (431, 105) 尺寸: 11×22 面积: 61 [区域内坐标: (6, 103)]
21:08:19.557/D:     - 边缘轮廓11: (472, 101) 尺寸: 14×20 面积: 3 [区域内坐标: (47, 99)]
21:08:19.558/D:     - 边缘轮廓12: (432, 105) 尺寸: 13×28 面积: 3 [区域内坐标: (7, 103)]
21:08:19.558/D:     - 边缘轮廓13: (477, 97) 尺寸: 67×44 面积: 83 [区域内坐标: (52, 95)]
21:08:19.559/D:     - 边缘轮廓14: (471, 83) 尺寸: 2×15 面积: 0 [区域内坐标: (46, 81)]
21:08:19.560/D:     - 边缘轮廓15: (490, 74) 尺寸: 8×1 面积: 0 [区域内坐标: (65, 72)]
21:08:19.560/D:     - 边缘轮廓16: (472, 105) 尺寸: 94×64 面积: 25 [区域内坐标: (47, 103)]
21:08:19.561/D:     - 边缘轮廓17: (474, 104) 尺寸: 97×82 面积: 163 [区域内坐标: (49, 102)]
21:08:19.562/D:     - 边缘轮廓18: (479, 104) 尺寸: 108×134 面积: 3 [区域内坐标: (54, 102)]
21:08:19.562/D:     - 边缘轮廓19: (480, 41) 尺寸: 110×14 面积: 9 [区域内坐标: (55, 39)]
21:08:19.563/D:     - 边缘轮廓20: (438, 21) 尺寸: 3×3 面积: 0 [区域内坐标: (13, 19)]
21:08:19.564/D:     - 边缘轮廓21: (440, 20) 尺寸: 18×14 面积: 107 [区域内坐标: (15, 18)]
21:08:19.565/D:     - 边缘轮廓22: (499, 19) 尺寸: 19×19 面积: 184 [区域内坐标: (74, 17)]
21:08:19.565/D:     - 边缘轮廓23: (474, 17) 尺寸: 22×18 面积: 243 [区域内坐标: (49, 15)]
21:08:19.566/D:     - 边缘轮廓24: (442, 18) 尺寸: 22×19 面积: 106 [区域内坐标: (17, 16)]
21:08:19.567/D:     - 边缘轮廓25: (525, 17) 尺寸: 12×22 面积: 193 [区域内坐标: (100, 15)]
21:08:19.571/D:   💾 边缘检测处理图已保存: /storage/emulated/0/Pictures/右广告_边缘检测_1752325699567_处理图.jpg
21:08:19.575/D:   💾 原始灰度处理图已保存: /storage/emulated/0/Pictures/右广告_原始灰度_1752325699567_处理图.jpg
21:08:19.579/D:   🎨 开始前景轮廓颜色增强...
21:08:19.581/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.582/D:       📊 前景色保护像素数量：0
21:08:19.583/D:     🎨 检测到需要增强的前景像素：0 个
21:08:19.585/D:     ℹ️ 未检测到需要增强的暗色前景像素
21:08:19.589/D:     ✨ 已对 9652 个其他前景像素进行亮度增强
21:08:19.596/D:     💾 前景颜色增强处理图已保存: /storage/emulated/0/Pictures/右广告_前景颜色增强_1752325699590_处理图.jpg
21:08:19.615/D:   🌪️ 开始杂乱背景优化分析...
21:08:19.634/D:   ⚠️ 检测到杂乱背景，杂乱等级: 3/5
21:08:19.636/D:   📊 杂乱特征: 轮廓较多(65个), 噪声干扰(30), 多轮廓分散(7个)
21:08:19.636/D:   🧹 策略1: 形态学清理 - 去除小噪声
21:08:19.643/D:   🎯 策略2: 目标区域保护 - 突出主要轮廓
21:08:19.647/D:   📊 策略3: 自适应阈值 - 增强对比度
21:08:19.648/D:     🔍 灰度图类型检查: 24 (目标: 0)
21:08:19.648/D:     ✅ 图像类型转换完成: 0
21:08:19.651/D:   🔍 策略4: 轮廓精炼 - 保留主要轮廓
21:08:19.652/D:     🔍 轮廓检测图像类型: 0 (目标: 0)
21:08:19.654/D:     📊 检测到 21 个原始轮廓
21:08:19.655/D:       ✅ 保留轮廓 1：面积 9 像素（无过滤）
21:08:19.656/D:       ✅ 保留轮廓 2：面积 933 像素（无过滤）
21:08:19.656/D:       ✅ 保留轮廓 3：面积 357 像素（无过滤）
21:08:19.657/D:       ✅ 保留轮廓 4：面积 59 像素（无过滤）
21:08:19.658/D:       ✅ 保留轮廓 5：面积 122 像素（无过滤）
21:08:19.658/D:       ✅ 保留轮廓 6：面积 3520 像素（无过滤）
21:08:19.659/D:       ✅ 保留轮廓 7：面积 11 像素（无过滤）
21:08:19.659/D:       ✅ 保留轮廓 8：面积 654 像素（无过滤）
21:08:19.660/D:       ✅ 保留轮廓 9：面积 1690 像素（无过滤）
21:08:19.660/D:       ✅ 保留轮廓 10：面积 1060 像素（无过滤）
21:08:19.661/D:       ✅ 保留轮廓 11：面积 254 像素（无过滤）
21:08:19.661/D:       ✅ 保留轮廓 12：面积 3 像素（无过滤）
21:08:19.662/D:       ✅ 保留轮廓 13：面积 0 像素（无过滤）
21:08:19.662/D:       ✅ 保留轮廓 14：面积 352 像素（无过滤）
21:08:19.663/D:       ✅ 保留轮廓 15：面积 411 像素（无过滤）
21:08:19.664/D:       ✅ 保留轮廓 16：面积 537 像素（无过滤）
21:08:19.664/D:       ✅ 保留轮廓 17：面积 337 像素（无过滤）
21:08:19.665/D:       ✅ 保留轮廓 18：面积 0 像素（无过滤）
21:08:19.665/D:       ✅ 保留轮廓 19：面积 79 像素（无过滤）
21:08:19.666/D:       ✅ 保留轮廓 20：面积 13 像素（无过滤）
21:08:19.667/D:       ✅ 保留轮廓 21：面积 9 像素（无过滤）
21:08:19.667/D:     📊 最终保留 21 个轮廓
21:08:19.676/D:     💾 轮廓精炼处理图已保存: /storage/emulated/0/Pictures/右广告_轮廓精炼_1752325699672_处理图.jpg
21:08:19.677/D:     📊 轮廓精炼: 21 → 21 个轮廓
21:08:19.678/D:   🔄 重新检测优化后的轮廓...
21:08:19.678/D:   🔄 在优化图像上重新检测轮廓...
21:08:19.679/D:     🔍 优化轮廓检测图像类型: 0 (目标: 0)
21:08:19.680/D:   📊 优化后检测到 21 个轮廓
21:08:19.688/D:     ✅ 轮廓 1 评分: 180 中心: (47,106)
21:08:19.689/D:     ✅ 轮廓 2 评分: 180 中心: (54,105)
21:08:19.689/D:     ✅ 轮廓 3 评分: 145 中心: (50,103)
21:08:19.690/D:     ✅ 轮廓 4 评分: 145 中心: (50,53)
21:08:19.691/D:     ✅ 轮廓 5 评分: 124 中心: (18,16)
21:08:19.691/D:     ✅ 轮廓 6 评分: 123 中心: (50,154)
21:08:19.692/D:     ✅ 轮廓 7 评分: 111 中心: (50,16)
21:08:19.692/D:   ✅ 生成了 7 个优化后的高质量轮廓
21:08:19.693/D:   ✨ 杂乱背景优化处理完成
21:08:19.694/D: ✅ 背景色已转换为黑色，前景内容已保留
21:08:19.694/D: 🔍 开始目标轮廓检测和线条过滤...
21:08:19.695/D: 📊 从原始图像检测到 7 个轮廓
21:08:19.695/D:   🔍 分析轮廓 1：68×94，面积：3520
21:08:19.696/D:     🔍 开始判断轮廓类型...
21:08:19.696/D:     📊 轮廓基本信息：68×94，面积：3520
21:08:19.697/D:     📝 形状描述：''
21:08:19.697/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.698/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.698/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.698/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.699/D:     🎯 ✅ 识别为目标轮廓
21:08:19.700/D:   🔍 分析轮廓 2：138×108，面积：1060
21:08:19.700/D:     🔍 开始判断轮廓类型...
21:08:19.701/D:     📊 轮廓基本信息：138×108，面积：1060
21:08:19.701/D:     📝 形状描述：''
21:08:19.702/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.702/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.703/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.703/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.704/D:     🎯 ✅ 识别为目标轮廓
21:08:19.705/D:   🔍 分析轮廓 3：85×99，面积：654
21:08:19.705/D:     🔍 开始判断轮廓类型...
21:08:19.706/D:     📊 轮廓基本信息：85×99，面积：654
21:08:19.706/D:     📝 形状描述：''
21:08:19.707/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.708/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.708/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.709/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.709/D:     🎯 ✅ 识别为目标轮廓
21:08:19.710/D:   🔍 分析轮廓 4：102×27，面积：1690
21:08:19.710/D:     🔍 开始判断轮廓类型...
21:08:19.711/D:     📊 轮廓基本信息：102×27，面积：1690
21:08:19.711/D:     📝 形状描述：''
21:08:19.712/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.712/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.712/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.713/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.714/D:     🎯 ✅ 识别为目标轮廓
21:08:19.714/D:   🔍 分析轮廓 5：22×25，面积：537
21:08:19.715/D:     🔍 开始判断轮廓类型...
21:08:19.715/D:     📊 轮廓基本信息：22×25，面积：537
21:08:19.716/D:     📝 形状描述：''
21:08:19.716/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.717/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.718/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.719/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.720/D:     🎯 ✅ 识别为目标轮廓
21:08:19.721/D:   🔍 分析轮廓 6：17×101，面积：933
21:08:19.721/D:     🔍 开始判断轮廓类型...
21:08:19.722/D:     📊 轮廓基本信息：17×101，面积：933
21:08:19.722/D:     📝 形状描述：''
21:08:19.723/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.724/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.725/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.726/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.726/D:     🎯 ✅ 识别为目标轮廓
21:08:19.727/D:   🔍 分析轮廓 7：21×25，面积：411
21:08:19.727/D:     🔍 开始判断轮廓类型...
21:08:19.728/D:     📊 轮廓基本信息：21×25，面积：411
21:08:19.728/D:     📝 形状描述：''
21:08:19.729/D:     🎯 轮廓类型判断：X号=false，播放号=false，箭头=false，是目标=false
21:08:19.729/D:     ⚠️ 无形状描述，采用宽松策略：true
21:08:19.729/D:     📊 基本特征：宽高比=true，面积=true，尺寸=true
21:08:19.730/D:     🛡️ 宽松策略判断：true（宽度>3，高度>3，面积>20）
21:08:19.731/D:     🎯 ✅ 识别为目标轮廓
21:08:19.732/D: 🧹 检测到 7 个目标轮廓，开始线条过滤...
21:08:19.733/D:   🧹 开始过滤轮廓附近的多余线条...
21:08:19.734/D:   📊 目标轮廓数量: 7，过滤半径: 20像素
21:08:19.735/D:   🎯 处理轮廓 1 中心点: (47, 106)
21:08:19.735/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.736/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.743/D:       📏 检测到 3 条直线
21:08:19.744/D:       🗑️ 移除干扰线条: (30,102) → (45,87) 长度:21
21:08:19.745/D:       🗑️ 移除干扰线条: (29,102) → (45,86) 长度:23
21:08:19.746/D:     🔧 策略3: 轮廓保护性清理
21:08:19.746/D:       🎨 开始创建前景色保护掩码...
21:08:19.747/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.748/D:       📊 前景色保护像素数量：0
21:08:19.749/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.749/D:   🎯 处理轮廓 2 中心点: (54, 105)
21:08:19.751/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.753/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.757/D:       📏 检测到 2 条直线
21:08:19.758/D:       🗑️ 移除干扰线条: (64,24) → (81,7) 长度:24
21:08:19.759/D:       🗑️ 移除干扰线条: (65,25) → (82,25) 长度:17
21:08:19.759/D:     🔧 策略3: 轮廓保护性清理
21:08:19.760/D:       🎨 开始创建前景色保护掩码...
21:08:19.761/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.761/D:       📊 前景色保护像素数量：0
21:08:19.762/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.763/D:   🎯 处理轮廓 3 中心点: (50, 103)
21:08:19.764/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.765/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.769/D:       📏 检测到 3 条直线
21:08:19.770/D:       🗑️ 移除干扰线条: (64,20) → (81,3) 长度:24
21:08:19.771/D:       🗑️ 移除干扰线条: (65,21) → (82,21) 长度:17
21:08:19.771/D:       🗑️ 移除干扰线条: (29,110) → (45,94) 长度:23
21:08:19.772/D:     🔧 策略3: 轮廓保护性清理
21:08:19.773/D:       🎨 开始创建前景色保护掩码...
21:08:19.774/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.775/D:       📊 前景色保护像素数量：0
21:08:19.776/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.778/D:   🎯 处理轮廓 4 中心点: (50, 53)
21:08:19.779/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.780/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.787/D:       📏 检测到 2 条直线
21:08:19.788/D:       🗑️ 移除干扰线条: (64,24) → (81,7) 长度:24
21:08:19.789/D:       🗑️ 移除干扰线条: (65,25) → (82,25) 长度:17
21:08:19.789/D:     🔧 策略3: 轮廓保护性清理
21:08:19.790/D:       🎨 开始创建前景色保护掩码...
21:08:19.791/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.792/D:       📊 前景色保护像素数量：0
21:08:19.793/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.794/D:   🎯 处理轮廓 5 中心点: (18, 16)
21:08:19.795/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.796/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.799/D:       📏 检测到 0 条直线
21:08:19.799/D:     🔧 策略3: 轮廓保护性清理
21:08:19.801/D:       🎨 开始创建前景色保护掩码...
21:08:19.802/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.802/D:       📊 前景色保护像素数量：0
21:08:19.804/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.805/D:   🎯 处理轮廓 6 中心点: (50, 154)
21:08:19.805/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.806/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.810/D:       📏 检测到 1 条直线
21:08:19.811/D:     🔧 策略3: 轮廓保护性清理
21:08:19.811/D:       🎨 开始创建前景色保护掩码...
21:08:19.814/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.815/D:       📊 前景色保护像素数量：0
21:08:19.816/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.817/D:   🎯 处理轮廓 7 中心点: (50, 16)
21:08:19.817/D:     🔧 策略1: 针对小线条的精准形态学清理
21:08:19.818/D:     🔧 策略2: 检测并移除干扰线条
21:08:19.822/D:       📏 检测到 0 条直线
21:08:19.822/D:     🔧 策略3: 轮廓保护性清理
21:08:19.823/D:       🎨 开始创建前景色保护掩码...
21:08:19.824/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.824/D:       📊 前景色保护像素数量：0
21:08:19.827/D:       ✅ 轮廓保护性清理完成（包含前景色保护）
21:08:19.828/D:   ✅ 轮廓附近线条过滤完成
21:08:19.828/D: ✅ 目标轮廓线条过滤完成，使用过滤后图像重新分析背景
21:08:19.828/D: 🔍 使用过滤后图像重新分析背景特征...
21:08:19.832/D:   📝 使用轮廓保护模式
21:08:19.834/D:   🔍 使用低阈值Canny边缘检测，适应暗色轮廓...
21:08:19.836/D:   🔍 开始检测边缘图中的轮廓坐标...
21:08:19.837/D:   📊 边缘检测发现 16 个轮廓
21:08:19.838/D:   📍 边缘检测全屏轮廓坐标信息:
21:08:19.841/D:     - 边缘轮廓1: (456, 117) 尺寸: 3×1 面积: 0 [区域内坐标: (31, 115)]
21:08:19.841/D:     - 边缘轮廓2: (456, 114) 尺寸: 1×1 面积: 0 [区域内坐标: (31, 112)]
21:08:19.842/D:     - 边缘轮廓3: (451, 113) 尺寸: 6×6 面积: 17 [区域内坐标: (26, 111)]
21:08:19.843/D:     - 边缘轮廓4: (428, 107) 尺寸: 5×1 面积: 0 [区域内坐标: (3, 105)]
21:08:19.844/D:     - 边缘轮廓5: (461, 110) 尺寸: 8×12 面积: 16 [区域内坐标: (36, 108)]
21:08:19.844/D:     - 边缘轮廓6: (428, 104) 尺寸: 5×2 面积: 0 [区域内坐标: (3, 102)]
21:08:19.845/D:     - 边缘轮廓7: (494, 110) 尺寸: 4×15 面积: 36 [区域内坐标: (69, 108)]
21:08:19.846/D:     - 边缘轮廓8: (480, 109) 尺寸: 10×17 面积: 43 [区域内坐标: (55, 107)]
21:08:19.846/D:     - 边缘轮廓9: (456, 102) 尺寸: 18×14 面积: 32 [区域内坐标: (31, 100)]
21:08:19.848/D:     - 边缘轮廓10: (471, 105) 尺寸: 7×22 面积: 73 [区域内坐标: (46, 103)]
21:08:19.848/D:     - 边缘轮廓11: (434, 106) 尺寸: 5×23 面积: 16 [区域内坐标: (9, 104)]
21:08:19.849/D:     - 边缘轮廓12: (490, 84) 尺寸: 16×17 面积: 57 [区域内坐标: (65, 82)]
21:08:19.850/D:     - 边缘轮廓13: (444, 18) 尺寸: 11×10 面积: 67 [区域内坐标: (19, 16)]
21:08:19.850/D:     - 边缘轮廓14: (499, 20) 尺寸: 18×17 面积: 155 [区域内坐标: (74, 18)]
21:08:19.851/D:     - 边缘轮廓15: (525, 18) 尺寸: 12×20 面积: 189 [区域内坐标: (100, 16)]
21:08:19.852/D:     - 边缘轮廓16: (475, 17) 尺寸: 21×18 面积: 224 [区域内坐标: (50, 15)]
21:08:19.856/D:   💾 边缘检测处理图已保存: /storage/emulated/0/Pictures/右广告_边缘检测_1752325699852_处理图.jpg
21:08:19.859/D:   💾 原始灰度处理图已保存: /storage/emulated/0/Pictures/右广告_原始灰度_1752325699852_处理图.jpg
21:08:19.861/D:   🎨 开始前景轮廓颜色增强...
21:08:19.862/D:       🎨 创建灰度前景色保护掩码：灰度值60 ±30
21:08:19.864/D:       📊 前景色保护像素数量：0
21:08:19.865/D:     🎨 检测到需要增强的前景像素：0 个
21:08:19.866/D:     ℹ️ 未检测到需要增强的暗色前景像素
21:08:19.868/D:     ✨ 已对 2824 个其他前景像素进行亮度增强
21:08:19.873/D:     💾 前景颜色增强处理图已保存: /storage/emulated/0/Pictures/右广告_前景颜色增强_1752325699869_处理图.jpg
21:08:19.895/D:   🌪️ 开始杂乱背景优化分析...
21:08:19.904/D:   ⚠️ 检测到杂乱背景，杂乱等级: 2/5
21:08:19.905/D:   📊 杂乱特征: 轮廓较多(45个), 多轮廓分散(8个)
21:08:19.905/D:   🧹 策略1: 形态学清理 - 去除小噪声
21:08:19.906/D:   🎯 策略2: 目标区域保护 - 突出主要轮廓
21:08:19.910/D:   📊 策略3: 自适应阈值 - 增强对比度
21:08:19.910/D:     🔍 灰度图类型检查: 24 (目标: 0)
21:08:19.911/D:     ✅ 图像类型转换完成: 0
21:08:19.913/D:   🔍 策略4: 轮廓精炼 - 保留主要轮廓
21:08:19.914/D:     🔍 轮廓检测图像类型: 0 (目标: 0)
21:08:19.915/D:     📊 检测到 52 个原始轮廓
21:08:19.916/D:       ✅ 保留轮廓 1：面积 7 像素（无过滤）
21:08:19.916/D:       ✅ 保留轮廓 2：面积 4 像素（无过滤）
21:08:19.917/D:       ✅ 保留轮廓 3：面积 2 像素（无过滤）
21:08:19.918/D:       ✅ 保留轮廓 4：面积 4 像素（无过滤）
21:08:19.918/D:       ✅ 保留轮廓 5：面积 0 像素（无过滤）
21:08:19.919/D:       ✅ 保留轮廓 6：面积 0 像素（无过滤）
21:08:19.919/D:       ✅ 保留轮廓 7：面积 1 像素（无过滤）
21:08:19.920/D:       ✅ 保留轮廓 8：面积 0 像素（无过滤）
21:08:19.921/D:       ✅ 保留轮廓 9：面积 12 像素（无过滤）
21:08:19.921/D:       ✅ 保留轮廓 10：面积 16 像素（无过滤）
21:08:19.922/D:       ✅ 保留轮廓 11：面积 0 像素（无过滤）
21:08:19.922/D:       ✅ 保留轮廓 12：面积 0 像素（无过滤）
21:08:19.923/D:       ✅ 保留轮廓 13：面积 0 像素（无过滤）
21:08:19.923/D:       ✅ 保留轮廓 14：面积 0 像素（无过滤）
21:08:19.924/D:       ✅ 保留轮廓 15：面积 0 像素（无过滤）
21:08:19.924/D:       ✅ 保留轮廓 16：面积 21 像素（无过滤）
21:08:19.925/D:       ✅ 保留轮廓 17：面积 63 像素（无过滤）
21:08:19.925/D:       ✅ 保留轮廓 18：面积 0 像素（无过滤）
21:08:19.926/D:       ✅ 保留轮廓 19：面积 181 像素（无过滤）
21:08:19.926/D:       ✅ 保留轮廓 20：面积 2 像素（无过滤）
21:08:19.927/D:       ✅ 保留轮廓 21：面积 36 像素（无过滤）
21:08:19.927/D:       ✅ 保留轮廓 22：面积 38 像素（无过滤）
21:08:19.928/D:       ✅ 保留轮廓 23：面积 0 像素（无过滤）
21:08:19.928/D:       ✅ 保留轮廓 24：面积 38 像素（无过滤）
21:08:19.929/D:       ✅ 保留轮廓 25：面积 2 像素（无过滤）
21:08:19.930/D:       ✅ 保留轮廓 26：面积 21 像素（无过滤）
21:08:19.930/D:       ✅ 保留轮廓 27：面积 0 像素（无过滤）
21:08:19.930/D:       ✅ 保留轮廓 28：面积 0 像素（无过滤）
21:08:19.931/D:       ✅ 保留轮廓 29：面积 323 像素（无过滤）
21:08:19.932/D:       ✅ 保留轮廓 30：面积 0 像素（无过滤）
21:08:19.932/D:       ✅ 保留轮廓 31：面积 0 像素（无过滤）
21:08:19.933/D:       ✅ 保留轮廓 32：面积 0 像素（无过滤）
21:08:19.933/D:       ✅ 保留轮廓 33：面积 0 像素（无过滤）
21:08:19.934/D:       ✅ 保留轮廓 34：面积 0 像素（无过滤）
21:08:19.934/D:       ✅ 保留轮廓 35：面积 0 像素（无过滤）
21:08:19.935/D:       ✅ 保留轮廓 36：面积 1 像素（无过滤）
21:08:19.935/D:       ✅ 保留轮廓 37：面积 0 像素（无过滤）
21:08:19.936/D:       ✅ 保留轮廓 38：面积 2 像素（无过滤）
21:08:19.936/D:       ✅ 保留轮廓 39：面积 0 像素（无过滤）
21:08:19.937/D:       ✅ 保留轮廓 40：面积 2 像素（无过滤）
21:08:19.937/D:       ✅ 保留轮廓 41：面积 136 像素（无过滤）
21:08:19.938/D:       ✅ 保留轮廓 42：面积 8 像素（无过滤）
21:08:19.939/D:       ✅ 保留轮廓 43：面积 1 像素（无过滤）
21:08:19.939/D:       ✅ 保留轮廓 44：面积 200 像素（无过滤）
21:08:19.939/D:       ✅ 保留轮廓 45：面积 172 像素（无过滤）
21:08:19.940/D:       ✅ 保留轮廓 46：面积 0 像素（无过滤）
21:08:19.940/D:       ✅ 保留轮廓 47：面积 0 像素（无过滤）
21:08:19.941/D:       ✅ 保留轮廓 48：面积 1 像素（无过滤）
21:08:19.941/D:       ✅ 保留轮廓 49：面积 0 像素（无过滤）
21:08:19.942/D:       ✅ 保留轮廓 50：面积 0 像素（无过滤）
21:08:19.942/D:       ✅ 保留轮廓 51：面积 0 像素（无过滤）
21:08:19.943/D:       ✅ 保留轮廓 52：面积 14132 像素（无过滤）
21:08:19.944/D:     📊 最终保留 52 个轮廓
21:08:19.954/D:     💾 轮廓精炼处理图已保存: /storage/emulated/0/Pictures/右广告_轮廓精炼_1752325699950_处理图.jpg
21:08:19.955/D:     📊 轮廓精炼: 52 → 52 个轮廓
21:08:19.956/D:   🔄 重新检测优化后的轮廓...
21:08:19.956/D:   🔄 在优化图像上重新检测轮廓...
21:08:19.957/D:     🔍 优化轮廓检测图像类型: 0 (目标: 0)
21:08:19.958/D:   📊 优化后检测到 52 个轮廓
21:08:19.964/D:     ✅ 轮廓 1 评分: 170 中心: (54,87)
21:08:19.965/D:     ✅ 轮廓 2 评分: 112 中心: (66,82)
21:08:19.966/D:     ✅ 轮廓 3 评分: 90 中心: (50,13)
21:08:19.966/D:     ✅ 轮廓 4 评分: 84 中心: (75,19)
21:08:19.967/D:     ✅ 轮廓 5 评分: 78 中心: (42,107)
21:08:19.967/D:     ✅ 轮廓 6 评分: 76 中心: (32,105)
21:08:19.968/D:     ✅ 轮廓 7 评分: 72 中心: (35,99)
21:08:19.968/D:   ✅ 生成了 7 个优化后的高质量轮廓
21:08:19.969/D:   ✨ 杂乱背景优化处理完成
21:08:19.969/D: ✅ 背景色已转换为黑色，前景内容已保留
21:08:19.970/D:   🌟 合并初步边缘检测轮廓信息: 25 个
21:08:19.971/D:   🌟 合并最终边缘检测轮廓信息: 16 个
21:08:19.971/D: 🌍 右广告 全屏轮廓坐标信息:
21:08:19.972/D:   🎯 轮廓 1 全屏位置:
21:08:19.972/D:     📍 区域内坐标: (54, 87)
21:08:19.973/D:     🌍 全屏坐标: (479, 89)
21:08:19.974/D:     📏 尺寸: 109×175
21:08:19.974/D:     📐 面积: 14132 像素
21:08:19.975/D:     🔍 形状: 播放号，大型轮廓，水平方向
21:08:19.975/D:     📋 区域信息:
21:08:19.976/D:       🏷️ 区域名称: 右广告
21:08:19.976/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:19.977/D:       📏 区域尺寸: 110×176
21:08:19.978/D:   🎯 轮廓 2 全屏位置:
21:08:19.978/D:     📍 区域内坐标: (66, 82)
21:08:19.978/D:     🌍 全屏坐标: (491, 84)
21:08:19.979/D:     📏 尺寸: 20×19
21:08:19.980/D:     📐 面积: 323 像素
21:08:19.980/D:     🔍 形状: X号，中型轮廓，垂直方向
21:08:19.981/D:     📋 区域信息:
21:08:19.981/D:       🏷️ 区域名称: 右广告
21:08:19.982/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:19.982/D:       📏 区域尺寸: 110×176
21:08:19.983/D:   🎯 轮廓 3 全屏位置:
21:08:19.984/D:     📍 区域内坐标: (50, 13)
21:08:19.984/D:     🌍 全屏坐标: (475, 15)
21:08:19.985/D:     📏 尺寸: 17×17
21:08:19.985/D:     📐 面积: 200 像素
21:08:19.986/D:     🔍 形状: X号，中型轮廓，倾斜 45°
21:08:19.987/D:     📋 区域信息:
21:08:19.987/D:       🏷️ 区域名称: 右广告
21:08:19.988/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:19.988/D:       📏 区域尺寸: 110×176
21:08:19.989/D:   🎯 轮廓 4 全屏位置:
21:08:19.989/D:     📍 区域内坐标: (75, 19)
21:08:19.990/D:     🌍 全屏坐标: (500, 21)
21:08:19.991/D:     📏 尺寸: 14×17
21:08:19.991/D:     📐 面积: 136 像素
21:08:19.992/D:     🔍 形状: 播放号，中型轮廓，垂直方向
21:08:19.992/D:     📋 区域信息:
21:08:19.992/D:       🏷️ 区域名称: 右广告
21:08:19.993/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:19.993/D:       📏 区域尺寸: 110×176
21:08:19.993/D:   🎯 轮廓 5 全屏位置:
21:08:19.994/D:     📍 区域内坐标: (42, 107)
21:08:19.994/D:     🌍 全屏坐标: (467, 109)
21:08:19.995/D:     📏 尺寸: 20×41
21:08:19.995/D:     📐 面积: 181 像素
21:08:19.995/D:     🔍 形状: 播放号，中型轮廓，垂直方向
21:08:19.996/D:     📋 区域信息:
21:08:19.996/D:       🏷️ 区域名称: 右广告
21:08:19.997/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:19.997/D:       📏 区域尺寸: 110×176
21:08:19.998/D:   🎯 轮廓 6 全屏位置:
21:08:19.998/D:     📍 区域内坐标: (32, 105)
21:08:19.999/D:     🌍 全屏坐标: (457, 107)
21:08:19.999/D:     📏 尺寸: 11×7
21:08:20.000/D:     📐 面积: 63 像素
21:08:20.000/D:     🔍 形状: >>号，小型轮廓，垂直方向
21:08:20.001/D:     📋 区域信息:
21:08:20.001/D:       🏷️ 区域名称: 右广告
21:08:20.002/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.002/D:       📏 区域尺寸: 110×176
21:08:20.003/D:   🎯 轮廓 7 全屏位置:
21:08:20.003/D:     📍 区域内坐标: (35, 99)
21:08:20.003/D:     🌍 全屏坐标: (460, 101)
21:08:20.004/D:     📏 尺寸: 16×28
21:08:20.005/D:     📐 面积: 21 像素
21:08:20.005/D:     🔍 形状: 播放号，小型轮廓，垂直方向
21:08:20.006/D:     📋 区域信息:
21:08:20.006/D:       🏷️ 区域名称: 右广告
21:08:20.006/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.007/D:       📏 区域尺寸: 110×176
21:08:20.008/D:   🌟 边缘检测轮廓:
21:08:20.008/D:   🎯 边缘1 全屏位置:
21:08:20.009/D:     📍 区域内坐标: (52, 173)
21:08:20.009/D:     🌍 全屏坐标: (477, 175)
21:08:20.010/D:     📏 尺寸: 104×6
21:08:20.010/D:     📐 面积: 2 像素
21:08:20.011/D:     🔍 形状: 边缘检测轮廓
21:08:20.011/D:     📋 区域信息:
21:08:20.011/D:       🏷️ 区域名称: 右广告
21:08:20.012/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.013/D:       📏 区域尺寸: 110×176
21:08:20.013/D:   🎯 边缘2 全屏位置:
21:08:20.013/D:     📍 区域内坐标: (106, 170)
21:08:20.014/D:     🌍 全屏坐标: (531, 172)
21:08:20.015/D:     📏 尺寸: 8×12
21:08:20.015/D:     📐 面积: 7 像素
21:08:20.018/D:     🔍 形状: 边缘检测轮廓
21:08:20.019/D:     📋 区域信息:
21:08:20.020/D:       🏷️ 区域名称: 右广告
21:08:20.020/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.021/D:       📏 区域尺寸: 110×176
21:08:20.022/D:   🎯 边缘3 全屏位置:
21:08:20.023/D:     📍 区域内坐标: (110, 163)
21:08:20.023/D:     🌍 全屏坐标: (535, 165)
21:08:20.024/D:     📏 尺寸: 1×1
21:08:20.025/D:     📐 面积: 0 像素
21:08:20.026/D:     🔍 形状: 边缘检测轮廓
21:08:20.027/D:     📋 区域信息:
21:08:20.027/D:       🏷️ 区域名称: 右广告
21:08:20.028/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.029/D:       📏 区域尺寸: 110×176
21:08:20.029/D:   🎯 边缘4 全屏位置:
21:08:20.030/D:     📍 区域内坐标: (53, 163)
21:08:20.031/D:     🌍 全屏坐标: (478, 165)
21:08:20.031/D:     📏 尺寸: 106×8
21:08:20.032/D:     📐 面积: 6 像素
21:08:20.033/D:     🔍 形状: 边缘检测轮廓
21:08:20.034/D:     📋 区域信息:
21:08:20.035/D:       🏷️ 区域名称: 右广告
21:08:20.036/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.037/D:       📏 区域尺寸: 110×176
21:08:20.038/D:   🎯 边缘5 全屏位置:
21:08:20.039/D:     📍 区域内坐标: (47, 135)
21:08:20.039/D:     🌍 全屏坐标: (472, 137)
21:08:20.041/D:     📏 尺寸: 93×11
21:08:20.043/D:     📐 面积: 8 像素
21:08:20.045/D:     🔍 形状: 边缘检测轮廓
21:08:20.045/D:     📋 区域信息:
21:08:20.047/D:       🏷️ 区域名称: 右广告
21:08:20.048/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.050/D:       📏 区域尺寸: 110×176
21:08:20.051/D:   🎯 边缘6 全屏位置:
21:08:20.052/D:     📍 区域内坐标: (63, 101)
21:08:20.053/D:     🌍 全屏坐标: (488, 103)
21:08:20.054/D:     📏 尺寸: 1×1
21:08:20.055/D:     📐 面积: 0 像素
21:08:20.055/D:     🔍 形状: 边缘检测轮廓
21:08:20.056/D:     📋 区域信息:
21:08:20.056/D:       🏷️ 区域名称: 右广告
21:08:20.057/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.058/D:       📏 区域尺寸: 110×176
21:08:20.058/D:   🎯 边缘7 全屏位置:
21:08:20.059/D:     📍 区域内坐标: (69, 103)
21:08:20.059/D:     🌍 全屏坐标: (494, 105)
21:08:20.059/D:     📏 尺寸: 18×22
21:08:20.060/D:     📐 面积: 94 像素
21:08:20.060/D:     🔍 形状: 边缘检测轮廓
21:08:20.061/D:     📋 区域信息:
21:08:20.062/D:       🏷️ 区域名称: 右广告
21:08:20.062/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.063/D:       📏 区域尺寸: 110×176
21:08:20.063/D:   🎯 边缘8 全屏位置:
21:08:20.064/D:     📍 区域内坐标: (51, 103)
21:08:20.065/D:     🌍 全屏坐标: (476, 105)
21:08:20.065/D:     📏 尺寸: 17×22
21:08:20.066/D:     📐 面积: 127 像素
21:08:20.066/D:     🔍 形状: 边缘检测轮廓
21:08:20.067/D:     📋 区域信息:
21:08:20.068/D:       🏷️ 区域名称: 右广告
21:08:20.068/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.069/D:       📏 区域尺寸: 110×176
21:08:20.070/D:   🎯 边缘9 全屏位置:
21:08:20.071/D:     📍 区域内坐标: (30, 103)
21:08:20.072/D:     🌍 全屏坐标: (455, 105)
21:08:20.072/D:     📏 尺寸: 18×22
21:08:20.073/D:     📐 面积: 309 像素
21:08:20.075/D:     🔍 形状: 边缘检测轮廓
21:08:20.075/D:     📋 区域信息:
21:08:20.076/D:       🏷️ 区域名称: 右广告
21:08:20.076/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.077/D:       📏 区域尺寸: 110×176
21:08:20.078/D:   🎯 边缘10 全屏位置:
21:08:20.079/D:     📍 区域内坐标: (6, 103)
21:08:20.080/D:     🌍 全屏坐标: (431, 105)
21:08:20.081/D:     📏 尺寸: 11×22
21:08:20.082/D:     📐 面积: 61 像素
21:08:20.083/D:     🔍 形状: 边缘检测轮廓
21:08:20.083/D:     📋 区域信息:
21:08:20.084/D:       🏷️ 区域名称: 右广告
21:08:20.085/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.086/D:       📏 区域尺寸: 110×176
21:08:20.086/D:   🎯 边缘11 全屏位置:
21:08:20.086/D:     📍 区域内坐标: (47, 99)
21:08:20.087/D:     🌍 全屏坐标: (472, 101)
21:08:20.088/D:     📏 尺寸: 14×20
21:08:20.088/D:     📐 面积: 3 像素
21:08:20.089/D:     🔍 形状: 边缘检测轮廓
21:08:20.090/D:     📋 区域信息:
21:08:20.090/D:       🏷️ 区域名称: 右广告
21:08:20.091/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.092/D:       📏 区域尺寸: 110×176
21:08:20.092/D:   🎯 边缘12 全屏位置:
21:08:20.092/D:     📍 区域内坐标: (7, 103)
21:08:20.093/D:     🌍 全屏坐标: (432, 105)
21:08:20.093/D:     📏 尺寸: 13×28
21:08:20.094/D:     📐 面积: 3 像素
21:08:20.095/D:     🔍 形状: 边缘检测轮廓
21:08:20.095/D:     📋 区域信息:
21:08:20.096/D:       🏷️ 区域名称: 右广告
21:08:20.096/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.097/D:       📏 区域尺寸: 110×176
21:08:20.098/D:   🎯 边缘13 全屏位置:
21:08:20.098/D:     📍 区域内坐标: (52, 95)
21:08:20.099/D:     🌍 全屏坐标: (477, 97)
21:08:20.103/D:     📏 尺寸: 67×44
21:08:20.104/D:     📐 面积: 83 像素
21:08:20.105/D:     🔍 形状: 边缘检测轮廓
21:08:20.107/D:     📋 区域信息:
21:08:20.108/D:       🏷️ 区域名称: 右广告
21:08:20.109/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.109/D:       📏 区域尺寸: 110×176
21:08:20.113/D:   🎯 边缘14 全屏位置:
21:08:20.114/D:     📍 区域内坐标: (46, 81)
21:08:20.117/D:     🌍 全屏坐标: (471, 83)
21:08:20.119/D:     📏 尺寸: 2×15
21:08:20.122/D:     📐 面积: 0 像素
21:08:20.124/D:     🔍 形状: 边缘检测轮廓
21:08:20.126/D:     📋 区域信息:
21:08:20.127/D:       🏷️ 区域名称: 右广告
21:08:20.130/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.132/D:       📏 区域尺寸: 110×176
21:08:20.136/D:   🎯 边缘15 全屏位置:
21:08:20.138/D:     📍 区域内坐标: (65, 72)
21:08:20.141/D:     🌍 全屏坐标: (490, 74)
21:08:20.144/D:     📏 尺寸: 8×1
21:08:20.146/D:     📐 面积: 0 像素
21:08:20.148/D:     🔍 形状: 边缘检测轮廓
21:08:20.149/D:     📋 区域信息:
21:08:20.151/D:       🏷️ 区域名称: 右广告
21:08:20.153/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.154/D:       📏 区域尺寸: 110×176
21:08:20.156/D:   🎯 边缘16 全屏位置:
21:08:20.157/D:     📍 区域内坐标: (47, 103)
21:08:20.158/D:     🌍 全屏坐标: (472, 105)
21:08:20.158/D:     📏 尺寸: 94×64
21:08:20.159/D:     📐 面积: 25 像素
21:08:20.159/D:     🔍 形状: 边缘检测轮廓
21:08:20.160/D:     📋 区域信息:
21:08:20.160/D:       🏷️ 区域名称: 右广告
21:08:20.161/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.161/D:       📏 区域尺寸: 110×176
21:08:20.162/D:   🎯 边缘17 全屏位置:
21:08:20.162/D:     📍 区域内坐标: (49, 102)
21:08:20.163/D:     🌍 全屏坐标: (474, 104)
21:08:20.163/D:     📏 尺寸: 97×82
21:08:20.163/D:     📐 面积: 163 像素
21:08:20.164/D:     🔍 形状: 边缘检测轮廓
21:08:20.164/D:     📋 区域信息:
21:08:20.165/D:       🏷️ 区域名称: 右广告
21:08:20.165/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.166/D:       📏 区域尺寸: 110×176
21:08:20.166/D:   🎯 边缘18 全屏位置:
21:08:20.167/D:     📍 区域内坐标: (54, 102)
21:08:20.167/D:     🌍 全屏坐标: (479, 104)
21:08:20.168/D:     📏 尺寸: 108×134
21:08:20.169/D:     📐 面积: 3 像素
21:08:20.169/D:     🔍 形状: 边缘检测轮廓
21:08:20.169/D:     📋 区域信息:
21:08:20.170/D:       🏷️ 区域名称: 右广告
21:08:20.170/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.171/D:       📏 区域尺寸: 110×176
21:08:20.171/D:   🎯 边缘19 全屏位置:
21:08:20.172/D:     📍 区域内坐标: (55, 39)
21:08:20.172/D:     🌍 全屏坐标: (480, 41)
21:08:20.173/D:     📏 尺寸: 110×14
21:08:20.173/D:     📐 面积: 9 像素
21:08:20.173/D:     🔍 形状: 边缘检测轮廓
21:08:20.174/D:     📋 区域信息:
21:08:20.175/D:       🏷️ 区域名称: 右广告
21:08:20.175/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.175/D:       📏 区域尺寸: 110×176
21:08:20.176/D:   🎯 边缘20 全屏位置:
21:08:20.176/D:     📍 区域内坐标: (13, 19)
21:08:20.177/D:     🌍 全屏坐标: (438, 21)
21:08:20.177/D:     📏 尺寸: 3×3
21:08:20.178/D:     📐 面积: 0 像素
21:08:20.178/D:     🔍 形状: 边缘检测轮廓
21:08:20.179/D:     📋 区域信息:
21:08:20.179/D:       🏷️ 区域名称: 右广告
21:08:20.180/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.180/D:       📏 区域尺寸: 110×176
21:08:20.181/D:   🎯 边缘21 全屏位置:
21:08:20.182/D:     📍 区域内坐标: (15, 18)
21:08:20.182/D:     🌍 全屏坐标: (440, 20)
21:08:20.183/D:     📏 尺寸: 18×14
21:08:20.183/D:     📐 面积: 107 像素
21:08:20.184/D:     🔍 形状: 边缘检测轮廓
21:08:20.184/D:     📋 区域信息:
21:08:20.185/D:       🏷️ 区域名称: 右广告
21:08:20.185/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.185/D:       📏 区域尺寸: 110×176
21:08:20.186/D:   🎯 边缘22 全屏位置:
21:08:20.186/D:     📍 区域内坐标: (74, 17)
21:08:20.186/D:     🌍 全屏坐标: (499, 19)
21:08:20.187/D:     📏 尺寸: 19×19
21:08:20.187/D:     📐 面积: 184 像素
21:08:20.189/D:     🔍 形状: 边缘检测轮廓
21:08:20.189/D:     📋 区域信息:
21:08:20.189/D:       🏷️ 区域名称: 右广告
21:08:20.190/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.190/D:       📏 区域尺寸: 110×176
21:08:20.190/D:   🎯 边缘23 全屏位置:
21:08:20.191/D:     📍 区域内坐标: (49, 15)
21:08:20.191/D:     🌍 全屏坐标: (474, 17)
21:08:20.191/D:     📏 尺寸: 22×18
21:08:20.197/D:     📐 面积: 243 像素
21:08:20.198/D:     🔍 形状: 边缘检测轮廓
21:08:20.198/D:     📋 区域信息:
21:08:20.199/D:       🏷️ 区域名称: 右广告
21:08:20.199/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.200/D:       📏 区域尺寸: 110×176
21:08:20.200/D:   🎯 边缘24 全屏位置:
21:08:20.200/D:     📍 区域内坐标: (17, 16)
21:08:20.201/D:     🌍 全屏坐标: (442, 18)
21:08:20.201/D:     📏 尺寸: 22×19
21:08:20.202/D:     📐 面积: 106 像素
21:08:20.202/D:     🔍 形状: 边缘检测轮廓
21:08:20.203/D:     📋 区域信息:
21:08:20.203/D:       🏷️ 区域名称: 右广告
21:08:20.204/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.205/D:       📏 区域尺寸: 110×176
21:08:20.205/D:   🎯 边缘25 全屏位置:
21:08:20.206/D:     📍 区域内坐标: (100, 15)
21:08:20.206/D:     🌍 全屏坐标: (525, 17)
21:08:20.207/D:     📏 尺寸: 12×22
21:08:20.207/D:     📐 面积: 193 像素
21:08:20.208/D:     🔍 形状: 边缘检测轮廓
21:08:20.208/D:     📋 区域信息:
21:08:20.209/D:       🏷️ 区域名称: 右广告
21:08:20.209/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.210/D:       📏 区域尺寸: 110×176
21:08:20.210/D:   🎯 边缘1 全屏位置:
21:08:20.211/D:     📍 区域内坐标: (31, 115)
21:08:20.211/D:     🌍 全屏坐标: (456, 117)
21:08:20.212/D:     📏 尺寸: 3×1
21:08:20.212/D:     📐 面积: 0 像素
21:08:20.213/D:     🔍 形状: 边缘检测轮廓
21:08:20.213/D:     📋 区域信息:
21:08:20.214/D:       🏷️ 区域名称: 右广告
21:08:20.214/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.215/D:       📏 区域尺寸: 110×176
21:08:20.215/D:   🎯 边缘2 全屏位置:
21:08:20.216/D:     📍 区域内坐标: (31, 112)
21:08:20.216/D:     🌍 全屏坐标: (456, 114)
21:08:20.217/D:     📏 尺寸: 1×1
21:08:20.217/D:     📐 面积: 0 像素
21:08:20.218/D:     🔍 形状: 边缘检测轮廓
21:08:20.218/D:     📋 区域信息:
21:08:20.219/D:       🏷️ 区域名称: 右广告
21:08:20.219/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.220/D:       📏 区域尺寸: 110×176
21:08:20.220/D:   🎯 边缘3 全屏位置:
21:08:20.221/D:     📍 区域内坐标: (26, 111)
21:08:20.222/D:     🌍 全屏坐标: (451, 113)
21:08:20.222/D:     📏 尺寸: 6×6
21:08:20.222/D:     📐 面积: 17 像素
21:08:20.223/D:     🔍 形状: 边缘检测轮廓
21:08:20.223/D:     📋 区域信息:
21:08:20.223/D:       🏷️ 区域名称: 右广告
21:08:20.224/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.224/D:       📏 区域尺寸: 110×176
21:08:20.225/D:   🎯 边缘4 全屏位置:
21:08:20.225/D:     📍 区域内坐标: (3, 105)
21:08:20.226/D:     🌍 全屏坐标: (428, 107)
21:08:20.226/D:     📏 尺寸: 5×1
21:08:20.226/D:     📐 面积: 0 像素
21:08:20.227/D:     🔍 形状: 边缘检测轮廓
21:08:20.227/D:     📋 区域信息:
21:08:20.228/D:       🏷️ 区域名称: 右广告
21:08:20.228/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.229/D:       📏 区域尺寸: 110×176
21:08:20.229/D:   🎯 边缘5 全屏位置:
21:08:20.230/D:     📍 区域内坐标: (36, 108)
21:08:20.230/D:     🌍 全屏坐标: (461, 110)
21:08:20.230/D:     📏 尺寸: 8×12
21:08:20.231/D:     📐 面积: 16 像素
21:08:20.232/D:     🔍 形状: 边缘检测轮廓
21:08:20.232/D:     📋 区域信息:
21:08:20.232/D:       🏷️ 区域名称: 右广告
21:08:20.233/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.233/D:       📏 区域尺寸: 110×176
21:08:20.234/D:   🎯 边缘6 全屏位置:
21:08:20.234/D:     📍 区域内坐标: (3, 102)
21:08:20.235/D:     🌍 全屏坐标: (428, 104)
21:08:20.235/D:     📏 尺寸: 5×2
21:08:20.236/D:     📐 面积: 0 像素
21:08:20.236/D:     🔍 形状: 边缘检测轮廓
21:08:20.236/D:     📋 区域信息:
21:08:20.237/D:       🏷️ 区域名称: 右广告
21:08:20.237/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.238/D:       📏 区域尺寸: 110×176
21:08:20.239/D:   🎯 边缘7 全屏位置:
21:08:20.239/D:     📍 区域内坐标: (69, 108)
21:08:20.239/D:     🌍 全屏坐标: (494, 110)
21:08:20.240/D:     📏 尺寸: 4×15
21:08:20.240/D:     📐 面积: 36 像素
21:08:20.241/D:     🔍 形状: 边缘检测轮廓
21:08:20.241/D:     📋 区域信息:
21:08:20.242/D:       🏷️ 区域名称: 右广告
21:08:20.242/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.243/D:       📏 区域尺寸: 110×176
21:08:20.243/D:   🎯 边缘8 全屏位置:
21:08:20.244/D:     📍 区域内坐标: (55, 107)
21:08:20.244/D:     🌍 全屏坐标: (480, 109)
21:08:20.245/D:     📏 尺寸: 10×17
21:08:20.245/D:     📐 面积: 43 像素
21:08:20.246/D:     🔍 形状: 边缘检测轮廓
21:08:20.246/D:     📋 区域信息:
21:08:20.247/D:       🏷️ 区域名称: 右广告
21:08:20.248/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.248/D:       📏 区域尺寸: 110×176
21:08:20.249/D:   🎯 边缘9 全屏位置:
21:08:20.249/D:     📍 区域内坐标: (31, 100)
21:08:20.250/D:     🌍 全屏坐标: (456, 102)
21:08:20.250/D:     📏 尺寸: 18×14
21:08:20.251/D:     📐 面积: 32 像素
21:08:20.251/D:     🔍 形状: 边缘检测轮廓
21:08:20.252/D:     📋 区域信息:
21:08:20.252/D:       🏷️ 区域名称: 右广告
21:08:20.253/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.253/D:       📏 区域尺寸: 110×176
21:08:20.253/D:   🎯 边缘10 全屏位置:
21:08:20.254/D:     📍 区域内坐标: (46, 103)
21:08:20.255/D:     🌍 全屏坐标: (471, 105)
21:08:20.255/D:     📏 尺寸: 7×22
21:08:20.255/D:     📐 面积: 73 像素
21:08:20.256/D:     🔍 形状: 边缘检测轮廓
21:08:20.256/D:     📋 区域信息:
21:08:20.257/D:       🏷️ 区域名称: 右广告
21:08:20.257/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.258/D:       📏 区域尺寸: 110×176
21:08:20.258/D:   🎯 边缘11 全屏位置:
21:08:20.259/D:     📍 区域内坐标: (9, 104)
21:08:20.259/D:     🌍 全屏坐标: (434, 106)
21:08:20.260/D:     📏 尺寸: 5×23
21:08:20.260/D:     📐 面积: 16 像素
21:08:20.261/D:     🔍 形状: 边缘检测轮廓
21:08:20.261/D:     📋 区域信息:
21:08:20.262/D:       🏷️ 区域名称: 右广告
21:08:20.262/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.263/D:       📏 区域尺寸: 110×176
21:08:20.263/D:   🎯 边缘12 全屏位置:
21:08:20.263/D:     📍 区域内坐标: (65, 82)
21:08:20.264/D:     🌍 全屏坐标: (490, 84)
21:08:20.265/D:     📏 尺寸: 16×17
21:08:20.265/D:     📐 面积: 57 像素
21:08:20.266/D:     🔍 形状: 边缘检测轮廓
21:08:20.266/D:     📋 区域信息:
21:08:20.267/D:       🏷️ 区域名称: 右广告
21:08:20.267/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.268/D:       📏 区域尺寸: 110×176
21:08:20.268/D:   🎯 边缘13 全屏位置:
21:08:20.269/D:     📍 区域内坐标: (19, 16)
21:08:20.269/D:     🌍 全屏坐标: (444, 18)
21:08:20.269/D:     📏 尺寸: 11×10
21:08:20.270/D:     📐 面积: 67 像素
21:08:20.270/D:     🔍 形状: 边缘检测轮廓
21:08:20.271/D:     📋 区域信息:
21:08:20.271/D:       🏷️ 区域名称: 右广告
21:08:20.272/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.272/D:       📏 区域尺寸: 110×176
21:08:20.273/D:   🎯 边缘14 全屏位置:
21:08:20.273/D:     📍 区域内坐标: (74, 18)
21:08:20.274/D:     🌍 全屏坐标: (499, 20)
21:08:20.274/D:     📏 尺寸: 18×17
21:08:20.275/D:     📐 面积: 155 像素
21:08:20.275/D:     🔍 形状: 边缘检测轮廓
21:08:20.276/D:     📋 区域信息:
21:08:20.276/D:       🏷️ 区域名称: 右广告
21:08:20.276/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.277/D:       📏 区域尺寸: 110×176
21:08:20.277/D:   🎯 边缘15 全屏位置:
21:08:20.278/D:     📍 区域内坐标: (100, 16)
21:08:20.278/D:     🌍 全屏坐标: (525, 18)
21:08:20.279/D:     📏 尺寸: 12×20
21:08:20.279/D:     📐 面积: 189 像素
21:08:20.280/D:     🔍 形状: 边缘检测轮廓
21:08:20.280/D:     📋 区域信息:
21:08:20.280/D:       🏷️ 区域名称: 右广告
21:08:20.281/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.281/D:       📏 区域尺寸: 110×176
21:08:20.282/D:   🎯 边缘16 全屏位置:
21:08:20.282/D:     📍 区域内坐标: (50, 15)
21:08:20.283/D:     🌍 全屏坐标: (475, 17)
21:08:20.283/D:     📏 尺寸: 21×18
21:08:20.283/D:     📐 面积: 224 像素
21:08:20.284/D:     🔍 形状: 边缘检测轮廓
21:08:20.285/D:     📋 区域信息:
21:08:20.285/D:       🏷️ 区域名称: 右广告
21:08:20.285/D:       📦 区域范围: (425, 2) 到 (535, 178)
21:08:20.286/D:       📏 区域尺寸: 110×176
21:08:20.287/D:   🌟 发现边缘检测轮廓，纳入点击建议:
21:08:20.287/D:     - 边缘1: (477, 175) 面积:2 形状:边缘检测轮廓
21:08:20.288/D:     - 边缘2: (531, 172) 面积:7 形状:边缘检测轮廓
21:08:20.288/D:     - 边缘3: (535, 165) 面积:0 形状:边缘检测轮廓
21:08:20.289/D:     - 边缘4: (478, 165) 面积:6 形状:边缘检测轮廓
21:08:20.289/D:     - 边缘5: (472, 137) 面积:8 形状:边缘检测轮廓
21:08:20.290/D:     - 边缘6: (488, 103) 面积:0 形状:边缘检测轮廓
21:08:20.290/D:     - 边缘7: (494, 105) 面积:94 形状:边缘检测轮廓
21:08:20.291/D:     - 边缘8: (476, 105) 面积:127 形状:边缘检测轮廓
21:08:20.291/D:     - 边缘9: (455, 105) 面积:309 形状:边缘检测轮廓
21:08:20.292/D:     - 边缘10: (431, 105) 面积:61 形状:边缘检测轮廓
21:08:20.293/D:     - 边缘11: (472, 101) 面积:3 形状:边缘检测轮廓
21:08:20.293/D:     - 边缘12: (432, 105) 面积:3 形状:边缘检测轮廓
21:08:20.294/D:     - 边缘13: (477, 97) 面积:83 形状:边缘检测轮廓
21:08:20.295/D:     - 边缘14: (471, 83) 面积:0 形状:边缘检测轮廓
21:08:20.295/D:     - 边缘15: (490, 74) 面积:0 形状:边缘检测轮廓
21:08:20.296/D:     - 边缘16: (472, 105) 面积:25 形状:边缘检测轮廓
21:08:20.296/D:     - 边缘17: (474, 104) 面积:163 形状:边缘检测轮廓
21:08:20.297/D:     - 边缘18: (479, 104) 面积:3 形状:边缘检测轮廓
21:08:20.297/D:     - 边缘19: (480, 41) 面积:9 形状:边缘检测轮廓
21:08:20.298/D:     - 边缘20: (438, 21) 面积:0 形状:边缘检测轮廓
21:08:20.298/D:     - 边缘21: (440, 20) 面积:107 形状:边缘检测轮廓
21:08:20.299/D:     - 边缘22: (499, 19) 面积:184 形状:边缘检测轮廓
21:08:20.299/D:     - 边缘23: (474, 17) 面积:243 形状:边缘检测轮廓
21:08:20.300/D:     - 边缘24: (442, 18) 面积:106 形状:边缘检测轮廓
21:08:20.300/D:     - 边缘25: (525, 17) 面积:193 形状:边缘检测轮廓
21:08:20.301/D:     - 边缘1: (456, 117) 面积:0 形状:边缘检测轮廓
21:08:20.302/D:     - 边缘2: (456, 114) 面积:0 形状:边缘检测轮廓
21:08:20.302/D:     - 边缘3: (451, 113) 面积:17 形状:边缘检测轮廓
21:08:20.303/D:     - 边缘4: (428, 107) 面积:0 形状:边缘检测轮廓
21:08:20.303/D:     - 边缘5: (461, 110) 面积:16 形状:边缘检测轮廓
21:08:20.304/D:     - 边缘6: (428, 104) 面积:0 形状:边缘检测轮廓
21:08:20.304/D:     - 边缘7: (494, 110) 面积:36 形状:边缘检测轮廓
21:08:20.305/D:     - 边缘8: (480, 109) 面积:43 形状:边缘检测轮廓
21:08:20.306/D:     - 边缘9: (456, 102) 面积:32 形状:边缘检测轮廓
21:08:20.306/D:     - 边缘10: (471, 105) 面积:73 形状:边缘检测轮廓
21:08:20.307/D:     - 边缘11: (434, 106) 面积:16 形状:边缘检测轮廓
21:08:20.308/D:     - 边缘12: (490, 84) 面积:57 形状:边缘检测轮廓
21:08:20.308/D:     - 边缘13: (444, 18) 面积:67 形状:边缘检测轮廓
21:08:20.309/D:     - 边缘14: (499, 20) 面积:155 形状:边缘检测轮廓
21:08:20.309/D:     - 边缘15: (525, 18) 面积:189 形状:边缘检测轮廓
21:08:20.310/D:     - 边缘16: (475, 17) 面积:224 形状:边缘检测轮廓
21:08:20.310/D:   💡 点击建议:
21:08:20.311/D:     🖱️ 推荐点击坐标: (479, 89)
21:08:20.311/D:     📝 AutoXjs点击代码: click(479, 89);
21:08:20.312/D:     🎯 选择依据: 主要检测 - 未知形状 (面积:14132)
21:08:20.312/D:     📋 备选坐标:
21:08:20.313/D:       2. (491, 84) - 主要检测 未知形状
21:08:20.313/D:       3. (455, 105) - 边缘检测 边缘检测轮廓
21:08:20.314/D: 🎯 根据背景色(黑色系)选择最佳增强策略...
21:08:20.315/D:   📝 黑色背景 → 使用亮度增强
21:08:20.315/D:     💡 亮度增强，输入图像通道数: 1
21:08:20.316/D:     🔘 灰度图像亮度增强
21:08:20.317/D:     ✅ 亮度增强完成，输出图像通道数: 1
21:08:20.317/D: ✅ 智能颜色增强完成
21:08:20.318/D:   🔍 轮廓检测，输入图像通道数: 1
21:08:20.318/D:   🔘 单通道图像已确保为CV_8UC1格式
21:08:20.319/D:   ✅ 轮廓检测完成，检测到 1 个轮廓
21:08:20.332/D: 处理图已保存: /storage/emulated/0/Pictures/右广告_线条过滤_1752325700321_处理图.jpg
21:08:20.333/D: 调试图片已保存: /storage/emulated/0/Pictures/右广告_线条过滤_1752325700321_原图调试.jpg
21:08:20.335/D: 检测结果: undefined
21:08:20.337/V: 
 ------------ 
 [ /storage/emulated/0/脚本/magic/脚本/算数/看广告.js ]运行结束，用时6.467000秒
