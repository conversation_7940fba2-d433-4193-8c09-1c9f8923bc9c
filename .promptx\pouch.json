{"currentState": "memory_saved", "stateHistory": [{"from": "initial", "command": "init", "timestamp": "2025-07-30T13:43:11.513Z", "args": [{"workingDirectory": "d:\\magic", "ideType": "cursor"}]}, {"from": "initialized", "command": "welcome", "timestamp": "2025-07-30T13:43:19.780Z", "args": []}, {"from": "service_discovery", "command": "action", "timestamp": "2025-07-30T13:43:24.949Z", "args": ["autoxjs-game-expert"]}, {"from": "role_activated_with_memory", "command": "recall", "timestamp": "2025-07-30T13:43:32.846Z", "args": ["autoxjs-game-expert"]}, {"from": "recalled-autoxjs-game-expert", "command": "recall", "timestamp": "2025-07-30T13:44:15.104Z", "args": ["autoxjs-game-expert", "规则"]}, {"from": "recalled-autoxjs-game-expert", "command": "recall", "timestamp": "2025-07-30T13:44:21.353Z", "args": ["autoxjs-game-expert"]}, {"from": "recalled-autoxjs-game-expert", "command": "remember", "timestamp": "2025-07-30T13:46:48.044Z", "args": ["autoxjs-game-expert", "Magic项目当前状态和结构：\n1. 项目基于AutoXjs ozobiozobi v6.5.8.17，采用Android原生XML布局\n2. 主要目录结构：main.js入口，ui/界面模块，脚本/逻辑模块，assets/资源，存储数据/配置\n3. 核心模块：主页、日志页、脚本配置页、菜单抽屉页，每个模块包含UI定义和业务逻辑\n4. 已完成基础架构：应用启动、权限检查、配置管理、日志系统、页面切换\n5. 技术规范：ES5语法，4空格缩进，中文注释，禁用WebView/HTML/CSS\n6. 已修复的关键问题：XML格式错误(逗号改空格)，API兼容性(ui.layout替代setContentView)，控件安全访问", "--tags", "项目状态 架构设计 技术规范"]}, {"from": "memory_saved", "command": "remember", "timestamp": "2025-07-30T13:47:03.970Z", "args": ["autoxjs-game-expert", "开发规范和习惯：\n1. 技术准确性第一：所有代码基于AutoXjs官方文档，严禁虚假信息，不确定时联网搜索确认\n2. 问题导向开发：专注解决实际问题，绝对禁止创建示例/测试/demo文件，开发前完整阅读相关代码\n3. 简洁代码规则：用最少代码解决最多问题，函数命名动词_名词格式，参数全部可调，功能整合，直线逻辑\n4. 安全编程模式：控件访问前检查存在性，完善try-catch异常处理，及时释放资源避免内存泄漏\n5. API使用规范：优先ui.layout()替代setContentView，使用attr()方法设置控件属性，XML属性用空格分隔不用逗号\n6. 模块化架构：UI定义与业务逻辑分离，统一导出\"布局\"属性，require/module.exports模块管理", "--tags", "开发规范 编程习惯 API规范"]}, {"from": "memory_saved", "command": "remember", "timestamp": "2025-07-30T13:47:15.306Z", "args": ["autoxjs-game-expert", "已修复的关键技术问题和解决方案：\n1. XML格式错误：padding/margin/stroke属性必须用空格分隔，不能用逗号，如\"8dp 4dp\"不是\"8dp,4dp\"\n2. API兼容性问题：ui.setContentView()不稳定，改用ui.layout()；控件方法如setText()改用attr()方法\n3. 模块导出不一致：统一使用\"布局\"作为XML布局导出属性名，避免属性名不匹配导致页面切换失败\n4. 控件访问安全性：直接访问控件可能空指针异常，必须先检查if(ui.控件名)再操作\n5. 权限检查优化：避免启动时触发权限请求导致崩溃，移除截图权限检查，延迟提示用户授权\n6. 全局错误处理：使用events.on(\"uncaughtException\")监听未捕获异常，安全记录日志", "--tags", "问题修复 技术经验 调试方案"]}, {"from": "memory_saved", "command": "recall", "timestamp": "2025-07-30T13:47:22.153Z", "args": ["autoxjs-game-expert"]}, {"from": "recalled-autoxjs-game-expert", "command": "remember", "timestamp": "2025-07-30T13:48:43.044Z", "args": ["autoxjs-game-expert", "PromptX项目配置和资源体系：\n1. 项目已配置完整的PromptX专业资源体系，包含6个专业资源\n2. 角色资源：autoxjs-game-expert专业角色，提供AutoXjs游戏辅助开发专业能力\n3. 执行模式：autoxjs-development-workflow开发工作流程，simple-code-rules简洁代码规则\n4. 知识库：autoxjs-ozobiozobi-expertise技术知识，magic-project-standards项目标准\n5. 思维模式：autoxjs-expert-thinking专家思维，指导AI的专业思考方式\n6. 记忆系统：已建立专业记忆库，支持跨会话记忆保持和精确检索\n7. 项目路径：D:\\magic，MCP实例：mcp-22460(cursor)，完全支持多项目隔离", "--tags", "PromptX配置 资源体系 记忆系统"]}], "lastUpdated": "2025-07-30T13:48:43.050Z"}