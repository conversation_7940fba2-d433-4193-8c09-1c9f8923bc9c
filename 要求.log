01:56:37.850/V: 开始运行[/storage/emulated/0/脚本/magic/脚本/算数/ocr看广告.js]
01:56:37.875/D: 
=== 看广告精准符号识别功能开始 ===
01:56:37.876/D: ⏰ 等待3秒后开始识别...
01:56:40.881/D: 📸 开始截图识别
01:56:41.128/D: <time>: 2025-08-03 01:56:41.126
	<line>: 1005
	<line>: 36 (看广告)
<msg>: 开始全屏OCR符号识别，配置: {"CPU核心数":4,"使用快速模型":false,"置信度阈值":0.1,"启用二值化":true,"二值化阈值":186,"二值化最大值":255,"反向二值化阈值":241,"反向二值化最大值":255,"反向二值化阈值2":120,"反向二值化最大值2":255,"启用图片匹配":true,"图片匹配阈值":0.8,"图片模板路径":"../../assets/算数游戏/广告/右广告","使用中心点坐标":true}

01:56:41.129/D: � 启动智能五重识别策略（单次截图复用）...
01:56:59.642/D: ⚡ 智能四重识别完成，最终结果: 9 个符号
01:56:59.644/D: 
📋 符号识别结果:
01:56:59.645/D:   🔣 识别到符号: 9 个
01:56:59.647/D: 🔣 符号详情: ，工书Q轻户外穿搭(139,50) 安装(472,51) 台(32,98) 针织轻盈微透的材质(272,758) C(524,947) Q、(406,48) ‘．(223,869) 家(284,866) 左播号-1(36,34)
01:56:59.830/D: ✅ 符号点击成功: "左播号-1" 坐标(36, 34)
