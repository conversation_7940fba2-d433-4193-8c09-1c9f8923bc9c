11:30:54.575/D: 🔄 启动三重识别策略...
11:30:54.576/D: 📸 第一步：原图识别
11:30:56.412/D: 🔍 原图OCR识别结果数量: 9
11:30:56.413/D: 📋 所有OCR识别内容:
11:30:56.416/D:   [1] "Demo×Progress" (置信度: 0.989)
11:30:56.416/D:   [2] "1区" (置信度: 0.637)
11:30:56.417/D:   [3] "Do×you×want×to×make" (置信度: 0.992)
11:30:56.418/D:   [4] "money×trading？" (置信度: 0.955)
11:30:56.421/D:   [5] "Gold" (置信度: 0.996)
11:30:56.424/D:   [6] "Bitcoin" (置信度: 0.999)
11:30:56.425/D:   [7] "Select×an×asset×to×start" (置信度: 0.968)
11:30:56.426/D:   [8] "Ad" (置信度: 0.958)
11:30:56.430/D:   [9] "O×PocketOption" (置信度: 0.928)
11:30:56.432/D: 🔍 分析 "Demo×Progress": ❌英文句子 (置信度: 0.989)
11:30:56.433/D: 🔍 分析 "1区": ❌中文 (置信度: 0.637)
11:30:56.435/D: 🔍 分析 "Do×you×want×to×make": ❌英文句子 (置信度: 0.992)
11:30:56.440/D: 🔍 分析 "money×trading？": ❌英文句子 (置信度: 0.955)
11:30:56.444/D: 🔍 分析 "Gold": ❌文字 (置信度: 0.996)
11:30:56.446/D: 🔍 分析 "Bitcoin": ❌长英文 (置信度: 0.999)
11:30:56.447/D: 🔍 分析 "Select×an×asset×to×start": ❌英文句子 (置信度: 0.968)
11:30:56.450/D: 🔍 分析 "Ad": ✅符号 (置信度: 0.958)
11:30:56.454/D: 🔍 分析 "O×PocketOption": ❌英文句子 (置信度: 0.928)
11:30:56.456/D: 📊 原图识别结果: 1 个符号
11:30:56.457/D: 🔣 原图识别符号:
11:30:56.457/D:   [1] "Ad" (置信度: 0.958)
11:30:56.458/D: ⏰ 延时1秒后进行二值化识别...
11:30:57.483/D: 📸 第二步：重新截图进行二值化识别
11:30:57.485/D: 📸 开始二值化处理，阈值: 186, 最大值: 255
11:30:57.514/D: ✅ 二值化处理完成
11:30:58.475/D: 🔍 二值化OCR识别结果数量: 11
11:30:58.476/D: 📋 所有OCR识别内容:
11:30:58.478/D:   [1] "moprogress" (置信度: 0.927)
11:30:58.479/D:   [2] "4" (置信度: 0.186)
11:30:58.479/D:   [3] "Do×you×want×to×make" (置信度: 0.988)
11:30:58.480/D:   [4] "money×trading？" (置信度: 0.979)
11:30:58.481/D:   [5] "皖" (置信度: 0.762)
11:30:58.482/D:   [6] "Gold" (置信度: 0.996)
11:30:58.483/D:   [7] "R" (置信度: 0.542)
11:30:58.484/D:   [8] "Bitcoin" (置信度: 0.999)
11:30:58.485/D:   [9] "Select×an×asset×to×start" (置信度: 0.972)
11:30:58.487/D:   [10] "O）×PocketOption" (置信度: 0.934)
11:30:58.488/D:   [11] "心" (置信度: 0.178)
11:30:58.489/D: 🔍 分析 "moprogress": ❌长英文 (置信度: 0.927)
11:30:58.490/D: 🔍 分析 "4": ✅符号 (置信度: 0.186)
11:30:58.492/D: 🔍 分析 "Do×you×want×to×make": ❌英文句子 (置信度: 0.988)
11:30:58.494/D: 🔍 分析 "money×trading？": ❌英文句子 (置信度: 0.979)
11:30:58.495/D: 🔍 分析 "皖": ❌中文 (置信度: 0.762)
11:30:58.496/D: 🔍 分析 "Gold": ❌文字 (置信度: 0.996)
11:30:58.496/D: 🔍 分析 "R": ✅符号 (置信度: 0.542)
11:30:58.498/D: 🔍 分析 "Bitcoin": ❌长英文 (置信度: 0.999)
11:30:58.498/D: 🔍 分析 "Select×an×asset×to×start": ❌英文句子 (置信度: 0.972)
11:30:58.500/D: 🔍 分析 "O）×PocketOption": ❌英文句子 (置信度: 0.934)
11:30:58.500/D: 🔍 分析 "心": ❌中文 (置信度: 0.178)
11:30:58.501/D: 📊 二值化识别结果: 2 个符号
11:30:58.502/D: 🔣 二值化识别符号:
11:30:58.505/D:   [1] "4" (置信度: 0.186)
11:30:58.507/D:   [2] "R" (置信度: 0.542)
11:30:58.510/D: ⏰ 延时1秒后进行反向二值化识别...
11:30:59.531/D: 📸 第三步：重新截图进行反向二值化识别
11:30:59.533/D: 📸 开始反向二值化处理，阈值: 241, 最大值: 255
11:30:59.549/D: ✅ 反向二值化处理完成
11:31:00.653/D: 🔍 反向二值化OCR识别结果数量: 11
11:31:00.654/D: 📋 所有OCR识别内容:
11:31:00.655/D:   [1] "Demo×Progress" (置信度: 0.984)
11:31:00.657/D:   [2] "1次" (置信度: 0.547)
11:31:00.658/D:   [3] "Do×you×want×to×make" (置信度: 0.994)
11:31:00.659/D:   [4] "money×trading？" (置信度: 0.932)
11:31:00.660/D:   [5] "中" (置信度: 0.577)
11:31:00.661/D:   [6] "Gold" (置信度: 0.988)
11:31:00.662/D:   [7] "BBitcoin" (置信度: 0.895)
11:31:00.664/D:   [8] "Selectanassetto×start" (置信度: 0.974)
11:31:00.666/D:   [9] "心" (置信度: 0.154)
11:31:00.678/D:   [10] "O×PocketOption" (置信度: 0.943)
11:31:00.679/D:   [11] "水" (置信度: 0.505)
11:31:00.680/D: 🔍 分析 "Demo×Progress": ❌英文句子 (置信度: 0.984)
11:31:00.681/D: 🔍 分析 "1次": ❌中文 (置信度: 0.547)
11:31:00.683/D: 🔍 分析 "Do×you×want×to×make": ❌英文句子 (置信度: 0.994)
11:31:00.684/D: 🔍 分析 "money×trading？": ❌英文句子 (置信度: 0.932)
11:31:00.684/D: 🔍 分析 "中": ❌中文 (置信度: 0.577)
11:31:00.685/D: 🔍 分析 "Gold": ❌文字 (置信度: 0.988)
11:31:00.686/D: 🔍 分析 "BBitcoin": ❌长英文 (置信度: 0.895)
11:31:00.687/D: 🔍 分析 "Selectanassetto×start": ❌英文句子 (置信度: 0.974)
11:31:00.688/D: 🔍 分析 "心": ❌中文 (置信度: 0.154)
11:31:00.689/D: 🔍 分析 "O×PocketOption": ❌英文句子 (置信度: 0.943)
11:31:00.691/D: 🔍 分析 "水": ❌中文 (置信度: 0.505)
11:31:00.693/D: 📊 反向二值化识别结果: 0 个符号
11:31:00.694/D:   ❌ 反向二值化未识别到符号
11:31:00.694/D: ⏰ 延时1秒后进行图片模板匹配...
11:31:01.697/D: 📸 第四步：重新截图进行图片模板匹配
11:31:01.707/D: 📸 开始图片模板匹配，阈值: 0.8
11:31:01.708/D: 📂 图片模板相对路径: ../../assets/算数游戏/广告/右广告
11:31:01.713/D: 📋 找到图片模板文件: 3 个
11:31:01.714/D: 📋 图片文件列表:
11:31:01.714/D:   [1] 2.png
11:31:01.715/D:   [2] 1.png
11:31:01.716/D:   [3] 3.png
11:31:01.717/D: 🔍 匹配模板: 2.png
11:31:02.044/D: ❌ 未匹配: 2.png
11:31:02.054/D: 🔍 匹配模板: 1.png
11:31:02.157/D: ❌ 未匹配: 1.png
11:31:02.160/D: 🔍 匹配模板: 3.png
11:31:02.492/D: ❌ 未匹配: 3.png
11:31:02.497/D: ✅ 图片模板匹配完成，匹配到 0 个符号
11:31:02.498/D: 📊 图片模板匹配结果: 0 个符号
11:31:02.499/D:   ❌ 图片模板匹配未识别到符号
11:31:02.500/D: 🔄 合并四重识别结果...
11:31:02.502/D: ✅ 四重识别完成，最终结果: 3 个符号
11:31:02.505/D: ✅ 符号识别成功！
11:31:02.506/D: 📊 识别到 3 个符号
11:31:02.506/D: 📝 完整符号: Ad 4 R
11:31:02.507/D: 🎯 平均置信度: 0.562
11:31:02.507/D: 
📋 符号识别结果:
11:31:02.508/D:   🔣 识别到符号: 3 个
11:31:02.510/D:   📊 原图识别: 1 个
11:31:02.511/D:   📊 二值化识别: 2 个
11:31:02.512/D:   📊 反向二值化识别: 0 个
11:31:02.513/D:   📊 图片模板匹配: 0 个
11:31:02.514/D:   🔄 四重合并后总数: 3 个
11:31:02.516/D: 
🔣 符号详情:
11:31:02.517/D:   [1] 符号: "Ad"
11:31:02.520/D:       置信度: 0.958
11:31:02.521/D:       中心坐标: (18, 939)
11:31:02.523/D:       区域: (11, 933, 25, 944)
11:31:02.524/D:       来源: 原图
11:31:02.527/D: 
11:31:02.528/D:   [2] 符号: "4"
11:31:02.529/D:       置信度: 0.186
11:31:02.529/D:       中心坐标: (488, 45)
11:31:02.530/D:       区域: (468, 28, 507, 62)
11:31:02.531/D:       来源: 二值化
11:31:02.531/D: 
11:31:02.532/D:   [3] 符号: "R"
11:31:02.533/D:       置信度: 0.542
11:31:02.534/D:       中心坐标: (178, 597)
11:31:02.535/D:       区域: (160, 581, 196, 613)
11:31:02.536/D:       来源: 二值化