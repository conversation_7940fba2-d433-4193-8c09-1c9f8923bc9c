# OCR符号点击功能开发方案

## 📋 项目概述

基于现有的OCR看广告.js五重识别功能，新增符号点击功能。当识别到特定符号后，根据预定义的区域规则自动点击符号的中心坐标。

## 🎯 需求分析

### 任务一：符号点击逻辑
1. **触发时机**：在`📋 符号识别结果:`和`🔣 符号详情:`输出完成后
2. **点击优先级**：
   - 优先点击：`图片匹配`和`图片匹配(替换)`来源的符号中心坐标
   - 次要点击：其他来源的符号（如果没有图片匹配符号）
3. **点击次数**：只点击一次，找到第一个匹配的符号即停止
4. **判断依据**：根据符号名称、中心坐标、来源进行匹配

### 任务二：区域定义管理
1. **新增函数**：`符号区域定义()` - 管理所有符号的区域配置
2. **变量化管理**：将OCR广告符号检测.log中的区域数据转换为可配置的变量
3. **便于维护**：方便查看和修改各个区域的符号定义

## 🔍 现有识别流程分析

根据ocr看广告.js文件分析，当前识别流程为**五重识别**：

1. **第一步**：原图识别
2. **第二步**：二值化识别（延时200ms）
3. **第三步**：反向二值化识别（延时200ms）
4. **第四步**：第二个反向二值化识别（延时200ms）
5. **第五步**：图片模板匹配（延时200ms）
6. **第六步**：五重合并结果

## 📊 符号区域完整分析

基于OCR广告符号检测.log全面分析，识别出以下区域和符号：

### 右上角区域（主要关闭区域）
- **符号类型**：X, X-1, X-2, X-4, X-5, X-6, X-10, X-11, 右双箭头, 右双箭头-1, 右双箭头-2, 播放号, K, Y, X0, >, 22等
- **中心坐标统计**：
  - **X坐标范围**：469-528 (±5容差：464-533)
  - **Y坐标范围**：22-98 (±5容差：17-103)
- **主要符号坐标分布**：
  - **X系列**：(489-511, 28-85)
  - **X-1**：(493, 45) 固定位置
  - **X-2**：(491-513, 27-82) 多位置
  - **X-4**：(512, 23) 固定位置
  - **X-5**：(495-513, 22-50) 两个位置
  - **X-6**：(497, 43) 固定位置
  - **X-10**：(505-511, 36-38) 两个位置
  - **X-11**：(490, 81) 固定位置
  - **右双箭头**：(495-498, 37-51) 多位置
  - **右双箭头-1**：(502, 35) 固定位置
  - **右双箭头-2**：(498, 37) 固定位置
  - **播放号**：(492, 41) 固定位置
  - **K**：(490, 41) 固定位置
  - **Y**：(475-496, 36-42) 两个位置
  - **X0**：(500, 31) 固定位置
  - **>**：(493-512, 26-38) 多位置
  - **22**：(469, 43) 固定位置

### 右中角区域（独立区域）
- **符号类型**：X-1, X-2, X-3, X-4, X-5, X-6, X-7, X-8, X-9, X-10, X-11（基于右广告目录图片）
- **中心坐标统计**：
  - **X坐标范围**：513 (±5容差：508-518)
  - **Y坐标范围**：311 (±5容差：306-316)
- **符号坐标**：
  - **X-3**：(513, 311) 固定位置（已确认）
  - **X-1到X-11**：预期在相同区域范围内出现

### 左下角区域（底部关闭区域）
- **符号类型**：Close
- **中心坐标统计**：
  - **X坐标范围**：115-246 (±5容差：110-251)
  - **Y坐标范围**：820-912 (±5容差：815-917)
- **符号坐标分布**：
  - **Close左侧**：(115-122, 820-911)
  - **Close右侧**：(246, 911-912)

### 顶部区域（顶部关闭区域）
- **符号类型**：Close
- **中心坐标统计**：
  - **X坐标范围**：365 (±5容差：360-370)
  - **Y坐标范围**：49 (±5容差：44-54)
- **符号坐标**：
  - **Close**：(365, 49) 固定位置

### 左上角区域（左上控制区域）
- **符号类型**：X, X-1, X-2, X-3, X-4, X-5, X-6, X-7, X-8, X-9, X-10, X-11, 双播放号, 右双箭头, 右双箭头-1, 右双箭头-2, 左播号-1, 播放号, C, C-1, K, 十, 公A（基于右广告目录图片扩展）
- **中心坐标统计**：
  - **X坐标范围**：29-48 (±5容差：24-53)
  - **Y坐标范围**：29-42 (±5容差：24-47)
- **符号坐标分布**：
  - **X**：(30-38, 32-35) 多位置
  - **X-1**：(30, 29) 固定位置（已确认）
  - **X-2到X-11**：预期在相同区域范围内出现
  - **X-9**：(30, 30) 固定位置（已确认）
  - **X-10**：(37, 36) 固定位置（已确认）
  - **双播放号**：预期位置(36-47, 34-42)
  - **右双箭头系列**：预期位置(30-45, 30-40)
  - **左播号-1**：(36, 34) 固定位置（已确认）
  - **播放号**：预期位置(35-45, 32-40)
  - **C**：(29, 33) 固定位置
  - **C-1**：(36, 36) 固定位置
  - **K**：(32-48, 31-41) 多位置
  - **十**：(30, 32) 固定位置
  - **公A(双播放号)**：(47, 42) 固定位置

### 横屏右上角区域（横屏模式专用）
- **符号类型**：X-1, X-2, X-3, X-4, X-5, X-6, X-7, X-8, X-9, X-10, X-11, 双播放号, 右双箭头, 右双箭头-1, 右双箭头-2, 左播号-1, 播放号, X, K（基于右广告目录图片扩展）
- **中心坐标统计**：
  - **X坐标范围**：37-923 (±5容差：32-928)
  - **Y坐标范围**：26-48 (±5容差：21-53)
- **符号坐标分布**：
  - **双播放号**：(923, 26) 横屏位置（已确认）
  - **X**：(913, 48) 横屏位置（已确认）
  - **X-1到X-11**：预期在横屏右上角区域(900-930, 25-50)
  - **右双箭头系列**：预期位置(910-925, 25-45)
  - **左播号-1**：预期位置(905-920, 30-45)
  - **播放号**：预期位置(915-925, 25-40)
  - **K**：(37, 40) 疑似错误位置（需排除）

### 错误坐标（需排除）
- **X-9**：坐标(528, 906) - 与X-10同时出现，明显错误
- **横屏K**：坐标(37, 40) - 在横屏区域但坐标异常

### 未知区域符号
- **v**：位置未明确，但出现这个符号点击他的中心坐标就行了

## 🎯 右广告目录符号完整列表

基于assets/算数游戏/广告/右广告目录中的图片文件，完整的符号列表如下：

### 图片模板符号列表
| 序号 | 符号名称 | 文件名 | 预期出现区域 | 优先级 |
|------|---------|--------|-------------|--------|
| 1 | X-1 | X-1.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 2 | X-2 | X-2.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 3 | X-3 | X-3.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 4 | X-4 | X-4.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 5 | X-5 | X-5.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 6 | X-6 | X-6.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 7 | X-7 | X-7.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 8 | X-8 | X-8.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 9 | X-9 | X-9.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 10 | X-10 | X-10.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 11 | X-11 | X-11.png | 右上角、右中角、左上角、横屏右上角 | 高 |
| 12 | 双播放号 | 双播放号.png | 左上角、横屏右上角 | 中 |
| 13 | 右双箭头 | 右双箭头.png | 右上角、左上角、横屏右上角 | 中 |
| 14 | 右双箭头-1 | 右双箭头-1.png | 右上角、左上角、横屏右上角 | 中 |
| 15 | 右双箭头-2 | 右双箭头-2.png | 右上角、左上角、横屏右上角 | 中 |
| 16 | 左播号-1 | 左播号-1.png | 左上角、横屏右上角 | 中 |
| 17 | 播放号 | 播放号.png | 右上角、左上角、横屏右上角 | 中 |

### 符号分类说明
- **X系列符号（X-1到X-11）**：主要的关闭按钮，优先级最高
- **播放控制符号**：双播放号、右双箭头系列、左播号-1、播放号
- **图片匹配优先级**：所有右广告目录中的符号都具有图片匹配优先权

## � 符号中心坐标统计表

### 右上角区域符号坐标统计
| 符号名称 | 中心坐标范围 | 出现次数 | 坐标容差范围(±5) | 备注 |
|---------|-------------|---------|-----------------|------|
| X | (489-511, 28-85) | 多次 | (484-516, 23-90) | 最常见符号 |
| X-1 | (493, 45) | 3次 | (488-498, 40-50) | 固定位置 |
| X-2 | (491-513, 27-82) | 8次 | (486-518, 22-87) | 多位置变化 |
| X-4 | (512, 23) | 3次 | (507-517, 18-28) | 固定位置 |
| X-5 | (495-513, 22-50) | 4次 | (490-518, 17-55) | 两个主要位置 |
| X-6 | (497, 43) | 2次 | (492-502, 38-48) | 固定位置 |
| X-10 | (505-511, 36-38) | 4次 | (500-516, 31-43) | 两个位置 |
| X-11 | (490, 81) | 1次 | (485-495, 76-86) | 固定位置 |
| 右双箭头 | (495-498, 37-51) | 5次 | (490-503, 32-56) | 两个主要位置 |
| 右双箭头-1 | (502, 35) | 1次 | (497-507, 30-40) | 固定位置 |
| 右双箭头-2 | (498, 37) | 2次 | (493-503, 32-42) | 固定位置 |
| 播放号 | (492, 41) | 1次 | (487-497, 36-46) | 固定位置 |
| K | (490, 41) | 1次 | (485-495, 36-46) | 固定位置 |
| Y | (475-496, 36-42) | 2次 | (470-501, 31-47) | 两个位置 |
| X0 | (500, 31) | 2次 | (495-505, 26-36) | 固定位置 |
| > | (493-512, 26-38) | 3次 | (488-517, 21-43) | 多位置 |
| 22 | (469, 43) | 1次 | (464-474, 38-48) | 固定位置 |

### 其他区域符号坐标统计
| 区域 | 符号名称 | 中心坐标 | 坐标容差范围(±5) | 备注 |
|------|---------|---------|-----------------|------|
| 右中角 | X-3 | (513, 311) | (508-518, 306-316) | 独立区域 |
| 左下角 | Close | (115-246, 820-912) | (110-251, 815-917) | 两个位置群 |
| 顶部 | Close | (365, 49) | (360-370, 44-54) | 固定位置 |
| 左上角 | X | (30-38, 32-35) | (25-43, 27-40) | 多位置 |
| 左上角 | X-1 | (30, 29) | (25-35, 24-34) | 固定位置 |
| 左上角 | X-9 | (30, 30) | (25-35, 25-35) | 固定位置 |
| 左上角 | X-10 | (37, 36) | (32-42, 31-41) | 固定位置 |
| 左上角 | 左播号-1 | (36, 34) | (31-41, 29-39) | 固定位置 |
| 左上角 | C | (29, 33) | (24-34, 28-38) | 固定位置 |
| 左上角 | C-1 | (36, 36) | (31-41, 31-41) | 固定位置 |
| 左上角 | K | (32-48, 31-41) | (27-53, 26-46) | 多位置 |
| 左上角 | 十 | (30, 32) | (25-35, 27-37) | 固定位置 |
| 左上角 | 公A | (47, 42) | (42-52, 37-47) | 固定位置 |
| 横屏右上角 | 双播放号 | (923, 26) | (918-928, 21-31) | 横屏专用 |
| 横屏右上角 | X | (913, 48) | (908-918, 43-53) | 横屏专用 |

### 坐标范围相近的符号群组
| 群组名称 | 符号列表 | 中心坐标范围 | 说明 |
|---------|---------|-------------|------|
| 右上角核心区 | X, X-1, X-6, 播放号, K | (490-497, 41-45) | 最密集区域 |
| 右上角顶部区 | X-4, X-5, > | (495-513, 22-31) | 顶部一排 |
| 右上角底部区 | X, X-11 | (489-491, 81-85) | 底部位置 |
| 左上角核心区 | X, X-1, X-9, 十, C | (29-32, 29-33) | 最密集区域 |
| 左下角分布区 | Close | (115-246, 820-912) | 分两个位置群 |

## �🛠️ 技术实现方案

### 方案A：新增独立函数（推荐）

#### 1. 新增函数：`符号区域定义()`
```javascript
function 符号区域定义() {
    // 坐标容差范围（±5像素）
    var 坐标容差 = 5;

    return {
        右上角区域: {
            符号列表: ["X", "X-1", "X-2", "X-4", "X-5", "X-6", "X-10", "X-11",
                      "右双箭头", "右双箭头-1", "右双箭头-2", "播放号", "K", "Y", "X0", ">", "22"],
            中心坐标范围: {
                minX: 469 - 坐标容差, maxX: 528 + 坐标容差,  // 464-533
                minY: 22 - 坐标容差, maxY: 98 + 坐标容差     // 17-103
            },
            优先符号: ["X-1", "X-2", "X-4", "X-5", "X-6", "X-10", "播放号", "右双箭头"],
            排除坐标: []
        },
        右中角区域: {
            符号列表: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11"],
            中心坐标范围: {
                minX: 513 - 坐标容差, maxX: 513 + 坐标容差,  // 508-518
                minY: 311 - 坐标容差, maxY: 311 + 坐标容差   // 306-316
            },
            优先符号: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11"],
            排除坐标: []
        },
        左下角区域: {
            符号列表: ["Close"],
            中心坐标范围: {
                minX: 115 - 坐标容差, maxX: 246 + 坐标容差,  // 110-251
                minY: 820 - 坐标容差, maxY: 912 + 坐标容差   // 815-917
            },
            优先符号: ["Close"],
            排除坐标: []
        },
        顶部区域: {
            符号列表: ["Close"],
            中心坐标范围: {
                minX: 365 - 坐标容差, maxX: 365 + 坐标容差,  // 360-370
                minY: 49 - 坐标容差, maxY: 49 + 坐标容差     // 44-54
            },
            优先符号: ["Close"],
            排除坐标: []
        },
        左上角区域: {
            符号列表: ["X", "X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号", "C", "C-1", "K", "十", "公A"],
            中心坐标范围: {
                minX: 29 - 坐标容差, maxX: 48 + 坐标容差,   // 24-53
                minY: 29 - 坐标容差, maxY: 42 + 坐标容差    // 24-47
            },
            优先符号: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号"],
            排除坐标: []
        },
        横屏右上角区域: {
            符号列表: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号", "X", "K"],
            中心坐标范围: {
                minX: 900 - 坐标容差, maxX: 930 + 坐标容差,  // 895-935
                minY: 26 - 坐标容差, maxY: 48 + 坐标容差     // 21-53
            },
            优先符号: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号", "X"],
            排除坐标: [
                {符号: "K", 坐标: {x: 37, y: 40}}  // 横屏区域中的异常K符号
            ]
        },
        // 全局错误坐标排除
        错误坐标排除: [
            {符号: "X-9", 坐标: {x: 528, y: 906}, 原因: "与X-10同时出现的异常坐标"},
            {符号: "K", 坐标: {x: 37, y: 40}, 原因: "横屏区域中的异常位置"}
        ]
    };
}
```

#### 2. 新增函数：`符号点击处理(识别结果)`
```javascript
function 符号点击处理(识别结果) {
    console.log("🎯 开始符号点击处理...");
    
    var 区域定义 = 符号区域定义();
    var 符号列表 = 识别结果.符号分类.符号;
    
    // 优先处理图片匹配符号
    var 图片匹配符号 = 符号列表.filter(function(符号) {
        return 符号.来源 === "图片匹配" || 符号.来源 === "图片匹配(替换)";
    });
    
    if (图片匹配符号.length > 0) {
        return 执行符号点击(图片匹配符号[0], "图片匹配优先");
    }
    
    // 处理其他符号
    for (var i = 0; i < 符号列表.length; i++) {
        var 符号 = 符号列表[i];
        if (是否为有效点击符号(符号, 区域定义)) {
            return 执行符号点击(符号, "OCR识别");
        }
    }
    
    console.log("❌ 未找到可点击的符号");
    return false;
}
```

#### 3. 修改主函数：在看广告()函数末尾添加点击处理
```javascript
// 在符号详情输出后添加
if (分类.符号.length > 0) {
    // 现有的符号详情输出代码...
    
    // 新增：符号点击处理
    符号点击处理(结果.识别结果);
}
```

### 方案B：集成到现有函数

直接在现有的符号详情输出代码后添加点击逻辑，不新增独立函数。

## 📝 输出内容设计

### 点击成功输出
```
🎯 开始符号点击处理...
🔍 检测到图片匹配符号: "X-2" 坐标(494, 40) 来源: 图片匹配(替换)
✅ 符号点击成功: "X-2" 中心坐标(494, 40) 点击类型: 图片匹配优先
📊 符号点击处理完成
```

### 点击失败输出
```
🎯 开始符号点击处理...
🔍 扫描识别符号: 13 个
❌ 未找到匹配的可点击符号
📊 符号点击处理完成
```

### 错误坐标排除输出
```
🎯 开始符号点击处理...
⚠️ 排除错误坐标: "X-9" 坐标(528, 906) 原因: 坐标异常
🔍 继续检测其他符号...
```

## 🎯 推荐方案

**推荐使用方案A**，理由如下：

1. **模块化设计**：新增独立函数便于维护和测试
2. **配置集中管理**：`符号区域定义()`函数集中管理所有区域配置
3. **易于扩展**：后续可以轻松添加新的符号和区域
4. **代码清晰**：主函数逻辑保持简洁，点击逻辑独立
5. **便于调试**：独立的点击处理函数便于单独测试和调试

## 📋 实施步骤

1. **第一步**：创建`符号区域定义()`函数
2. **第二步**：创建`符号点击处理()`函数
3. **第三步**：创建辅助函数（`是否为有效点击符号()`, `执行符号点击()`等）
4. **第四步**：在主函数中集成点击处理逻辑
5. **第五步**：添加详细的输出日志
6. **第六步**：测试和优化

## ⚠️ 注意事项

1. **错误坐标排除**：必须排除坐标(528, 906)等明显错误的坐标
2. **点击次数控制**：确保只点击一次，避免重复点击
3. **优先级严格执行**：图片匹配符号绝对优先
4. **坐标有效性检查**：点击前验证坐标是否在屏幕范围内
5. **异常处理**：完善的错误处理和日志记录

## 🔧 配置参数

建议在默认配置中添加点击相关参数：
```javascript
var 默认配置 = {
    // ... 现有配置
    启用符号点击: true,           // 是否启用符号点击功能
    点击延时: 500,               // 点击后延时时间(ms)
    坐标有效性检查: true,         // 是否检查坐标有效性
    排除错误坐标: true           // 是否排除已知错误坐标
};
```

---

**方案制定完成，等待执行指令！**
