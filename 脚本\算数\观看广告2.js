/**
 * 观看广告.js v18.0.1 - 单函数多参数接口广告识别系统
 *
 * 功能特点：
 * 1. 🔍 高精度边缘检测 - 专门检测低对比度轮廓（如#3C3C3C播放按钮）
 * 2. 🎯 智能轮廓分析 - 自动识别播放号、>>号、X号等目标轮廓
 * 3. 🌍 统一中心点坐标 - 边缘检测和图片匹配都返回中心点坐标
 * 4. 📊 多候选推荐 - 提供主要和备选点击坐标
 * 5. 💾 调试图片保存 - 保存处理过程图片便于分析
 * 6. 🔧 单函数多参数接口 - 通过参数组合实现不同功能
 *
 * 坐标计算方式：
 * - 边缘检测轮廓：返回轮廓外接矩形的中心点坐标
 * - 图片模板匹配：返回模板图片的中心点坐标（从左上角+宽高/2计算）
 * - 全屏坐标转换：区域内中心点坐标 + 区域偏移坐标 = 全屏中心点坐标
 *
 * 相似度计算方式：
 * - 使用images.matchTemplate()进行匹配（返回真实相似度数值）
 * - matchTemplate返回MatchingResult对象，包含matches数组
 * - 每个match包含point坐标和similarity真实相似度
 * - 基于看广告.js的API研究，实现真实相似度获取
 *
 * 接口设计：
 * - 看广告(区域名称, 保存调试图片, 检测配置) - 完整检测
 * - 看广告(区域图像, 区域坐标, 区域名称, 保存调试图片, 检测配置) - 边缘检测
 * - 看广告(边缘Mat, 灰度Mat, 区域名称) - 保存调试图片
 *
 * 基于AutoXjs ozobiozobi v6.5.8.17
 * 适配Android 9+系统，主要在雷电模拟器环境下测试（540x960，DPI：240）
 */

// 🔧 初始化OpenCV并导入相关类
runtime.images.initOpenCvIfNeeded();

// 导入OpenCV相关类
importClass(org.opencv.core.Mat);
importClass(org.opencv.core.MatOfPoint);
importClass(org.opencv.core.MatOfPoint2f);
importClass(org.opencv.core.Scalar);
importClass(org.opencv.core.Point);
importClass(org.opencv.core.Size);
importClass(org.opencv.core.Rect);
importClass(org.opencv.core.RotatedRect);
importClass(org.opencv.core.CvType);
importClass(org.opencv.imgproc.Imgproc);
importClass(org.opencv.imgcodecs.Imgcodecs);
importClass(org.opencv.core.Core);
importClass(java.util.ArrayList);
importClass(java.util.List);

/**
 * 看广告 - 单函数多接口（直接参数命名）
 *
 * 接口1：完整检测 - 看广告(区域名称, 选项)
 * 接口2：边缘检测 - 看广告(区域图像, 区域坐标, 选项) [包含轮廓特征分析]
 * 接口3：保存图片 - 看广告(边缘Mat, 灰度Mat, 区域名称)
 * 接口4：图片匹配 - 看广告("图片匹配", 匹配选项)
 */

/**
 * 模板预处理 - 将广告图片转换为边缘模板
 *
 * @param {string} 广告目录路径 - 广告目录的路径，默认为标准路径
 * @param {object} 处理选项 - 边缘检测参数配置
 * @returns {object} 处理结果统计
 */

/**
 * 图片匹配 - 使用模板匹配查找广告元素
 *
 * @param {Image} 目标图像 - 要匹配的目标图像
 * @param {object} 匹配选项 - 匹配参数配置
 * @returns {object} 匹配结果
 */
function 看广告(区域名称或图像或Mat, 选项或坐标或灰度Mat, 选项或区域名称) {
    // 🔧 智能接口识别：根据参数类型和数量判断调用模式
    if (区域名称或图像或Mat === "图片匹配") {
        // ==================== 接口4：图片匹配 ====================
        // 看广告("图片匹配", 匹配选项)
        return 图片匹配测试功能(选项或坐标或灰度Mat);

    } else if (arguments.length === 3 && 区域名称或图像或Mat && 区域名称或图像或Mat.constructor && 区域名称或图像或Mat.constructor.name === 'Mat') {
        // ==================== 接口3：保存调试图片 ====================
        // 看广告(边缘Mat, 灰度Mat, 区域名称)

        try {
            // 使用固定文件名，覆盖保存
            var 边缘保存路径 = "/storage/emulated/0/Pictures/" + 选项或区域名称 + "_边缘检测.jpg";
            Imgcodecs.imwrite(边缘保存路径, 区域名称或图像或Mat);
            console.log("  💾 边缘检测处理图已保存: " + 边缘保存路径);

            // 保存原始灰度图
            var 灰度保存路径 = "/storage/emulated/0/Pictures/" + 选项或区域名称 + "_原始灰度.jpg";
            Imgcodecs.imwrite(灰度保存路径, 选项或坐标或灰度Mat);
            console.log("  💾 原始灰度处理图已保存: " + 灰度保存路径);

            return {
                成功: true,
                边缘保存路径: 边缘保存路径,
                灰度保存路径: 灰度保存路径
            };

        } catch (saveError) {
            console.error("  ❌ 保存OpenCV调试图片失败: " + saveError);
            return {
                成功: false,
                错误: saveError.toString()
            };
        }

    } else if (arguments.length >= 3 && typeof 区域名称或图像或Mat === 'object' && 区域名称或图像或Mat.getWidth) {
        // ==================== 接口2：边缘检测 ====================
        // 看广告(区域图像, 区域坐标, 选项)
        var 选项 = 选项或区域名称 || {};

        // 从选项中提取参数
        var 区域名称 = 选项.区域名称 || "未命名区域";
        var 保存调试图片参数 = 选项.保存调试图片 !== false;
        var 检测配置参数 = 选项.检测配置 || {};

        try {
            console.log("  🔍 开始OpenCV边缘检测轮廓分析...");

            // 默认检测配置 - 不进行轮廓过滤
            var 配置 = {
                Canny低阈值: 检测配置参数.Canny低阈值 || 20,
                Canny高阈值: 检测配置参数.Canny高阈值 || 60,
                高斯模糊核心: 检测配置参数.高斯模糊核心 || 3,
                启用轮廓过滤: 检测配置参数.启用轮廓过滤 !== undefined ? 检测配置参数.启用轮廓过滤 : false,  // 默认不过滤
                最小轮廓面积: 检测配置参数.最小轮廓面积 || 0,      // 设为0，不过滤小轮廓
                最大轮廓面积: 检测配置参数.最大轮廓面积 || 999999   // 设为极大值，不过滤大轮廓
            };

            // 1. 转换为OpenCV Mat格式
            var 原始Mat = 区域名称或图像或Mat.getMat();

            // 2. 转换为灰度图
            var 灰度Mat = new Mat();
            Imgproc.cvtColor(原始Mat, 灰度Mat, Imgproc.COLOR_BGR2GRAY);

            // 3. 高斯模糊减少噪声
            var 模糊Mat = new Mat();
            Imgproc.GaussianBlur(灰度Mat, 模糊Mat, new Size(配置.高斯模糊核心, 配置.高斯模糊核心), 0);

            // 4. 🔍 低阈值边缘检测 - 专门检测低对比度轮廓（如#3C3C3C）
            var 边缘Mat = new Mat();
            console.log("  🔍 使用低阈值Canny边缘检测，适应暗色轮廓...");
            console.log("    📊 Canny参数: 低阈值=" + 配置.Canny低阈值 + ", 高阈值=" + 配置.Canny高阈值);
            Imgproc.Canny(模糊Mat, 边缘Mat, 配置.Canny低阈值, 配置.Canny高阈值);

            // 5. 🔍 检测边缘图中的轮廓
            console.log("  🔍 开始检测边缘图中的轮廓坐标...");
            var 轮廓列表 = new ArrayList();
            var 层次结构 = new Mat();
            Imgproc.findContours(边缘Mat, 轮廓列表, 层次结构, Imgproc.RETR_EXTERNAL, Imgproc.CHAIN_APPROX_SIMPLE);

            console.log("  📊 边缘检测发现 " + 轮廓列表.size() + " 个轮廓");

            // 6. 🎯 收集和分析边缘轮廓信息
            var 边缘轮廓信息列表 = [];
            var 有效轮廓信息列表 = [];

            if (轮廓列表.size() > 0) {
                console.log("  📍 边缘检测全屏轮廓坐标信息:");
                if (配置.启用轮廓过滤) {
                    console.log("  🔧 过滤条件: 最小面积=" + 配置.最小轮廓面积 + ", 最大面积=" + 配置.最大轮廓面积);
                } else {
                    console.log("  🔧 轮廓过滤: 已禁用，显示所有轮廓");
                }

                var 过滤统计 = {总数: 轮廓列表.size(), 有效: 0, 过小: 0, 过大: 0};

                for (var i = 0; i < 轮廓列表.size(); i++) {
                    var 轮廓 = 轮廓列表.get(i);
                    var 轮廓面积 = Imgproc.contourArea(轮廓);

                    // 如果启用过滤，则进行面积过滤
                    if (配置.启用轮廓过滤) {
                        if (轮廓面积 < 配置.最小轮廓面积) {
                            过滤统计.过小++;
                            continue;
                        }
                        if (轮廓面积 > 配置.最大轮廓面积) {
                            过滤统计.过大++;
                            continue;
                        }
                    }

                    过滤统计.有效++;

                    // 计算轮廓的外接矩形
                    var 外接矩形 = Imgproc.boundingRect(轮廓);
                    var 区域内中心X = Math.round(外接矩形.x + 外接矩形.width / 2);
                    var 区域内中心Y = Math.round(外接矩形.y + 外接矩形.height / 2);

                    // 🌍 转换为全屏坐标
                    var 全屏中心X = 选项或坐标或灰度Mat[0] + 区域内中心X;
                    var 全屏中心Y = 选项或坐标或灰度Mat[1] + 区域内中心Y;

                    // 🎯 计算轮廓特征
                    var 长宽比 = 外接矩形.width / 外接矩形.height;
                    var 面积比 = 轮廓面积 / (外接矩形.width * 外接矩形.height);

                    // 形状分类
                    var 形状分类;
                    if (Math.abs(长宽比 - 1.0) < 0.3) {
                        形状分类 = "正方形";
                    } else if (长宽比 > 1.5 || 长宽比 < 0.67) {
                        形状分类 = "长方形";
                    } else if (面积比 < 0.4) {
                        形状分类 = "线条";
                    } else {
                        形状分类 = "复杂";
                    }

                    // 面积等级
                    var 面积等级 = 轮廓面积 < 50 ? "极小" :
                                 轮廓面积 < 200 ? "小" :
                                 轮廓面积 < 500 ? "中" :
                                 轮廓面积 < 1000 ? "大" : "极大";

                    // 位置特征
                    var 位置特征 = "";
                    if (全屏中心X > 480 && 全屏中心Y < 200) 位置特征 += "右上 ";
                    if (全屏中心X < 60 && 全屏中心Y < 200) 位置特征 += "左上 ";
                    if (全屏中心Y > 700) 位置特征 += "底部 ";
                    if (全屏中心X > 200 && 全屏中心X < 340) 位置特征 += "中央 ";
                    if (位置特征 === "") 位置特征 = "其他";

                    // 🎯 收集边缘轮廓信息
                    var 轮廓信息 = {
                        索引: "边缘" + (i + 1),
                        中心点: {
                            x: 区域内中心X,
                            y: 区域内中心Y
                        },
                        全屏坐标: {
                            x: 全屏中心X,
                            y: 全屏中心Y
                        },
                        尺寸: {
                            宽度: 外接矩形.width,
                            高度: 外接矩形.height
                        },
                        面积: Math.round(轮廓面积),
                        形状: "边缘检测轮廓",
                        来源: "边缘检测",
                        优先级: 轮廓面积,
                        // 新增特征信息
                        长宽比: Math.round(长宽比 * 100) / 100,
                        面积比: Math.round(面积比 * 100) / 100,
                        形状分类: 形状分类,
                        面积等级: 面积等级,
                        位置特征: 位置特征.trim()
                    };

                    边缘轮廓信息列表.push(轮廓信息);
                    有效轮廓信息列表.push(轮廓信息);  // 所有轮廓都视为有效

                    console.log("    - 边缘轮廓" + (i + 1) + ": (" + 全屏中心X + ", " + 全屏中心Y + ") 尺寸: " +
                               外接矩形.width + "×" + 外接矩形.height + " 面积: " + Math.round(轮廓面积) +
                               " 长宽比: " + 轮廓信息.长宽比 + " 形状: " + 形状分类 + " 等级: " + 面积等级 +
                               " 位置: " + 轮廓信息.位置特征 + " [区域内坐标: (" + 区域内中心X + ", " + 区域内中心Y + ")]");
                }

                // 输出统计信息
                if (配置.启用轮廓过滤) {
                    console.log("  📊 轮廓过滤统计: 总数=" + 过滤统计.总数 + " 有效=" + 过滤统计.有效 +
                               " 过小=" + 过滤统计.过小 + " 过大=" + 过滤统计.过大);
                } else {
                    console.log("  📊 轮廓统计: 总数=" + 过滤统计.总数 + " 显示=" + 过滤统计.有效 + " (无过滤)");
                }

            } else {
                console.log("  ⚠️ 边缘检测未发现任何轮廓");
            }



            // 7. 💾 保存调试图片
            if (保存调试图片参数) {
                // 直接保存调试图片，避免递归调用
                try {
                    // 使用固定文件名，覆盖保存
                    var 边缘保存路径 = "/storage/emulated/0/Pictures/" + 区域名称 + "_边缘检测.jpg";
                    Imgcodecs.imwrite(边缘保存路径, 边缘Mat);
                    console.log("  💾 边缘检测处理图已保存: " + 边缘保存路径);

                    // 保存原始灰度图
                    var 灰度保存路径 = "/storage/emulated/0/Pictures/" + 区域名称 + "_原始灰度.jpg";
                    Imgcodecs.imwrite(灰度保存路径, 灰度Mat);
                    console.log("  💾 原始灰度处理图已保存: " + 灰度保存路径);

                } catch (saveError) {
                    console.error("  ❌ 保存OpenCV调试图片失败: " + saveError);
                }
            }

            // 8. 🎯 生成推荐点击坐标
            var 推荐点击坐标 = null;

            if (有效轮廓信息列表.length > 0) {
                // 按面积排序，选择最大的轮廓作为推荐坐标
                有效轮廓信息列表.sort(function(a, b) { return b.面积 - a.面积; });

                推荐点击坐标 = 有效轮廓信息列表[0].全屏坐标;

                console.log("  💡 点击建议:");
                console.log("    🖱️ 推荐点击坐标: (" + 推荐点击坐标.x + ", " + 推荐点击坐标.y + ")");
                console.log("    📝 AutoXjs点击代码: click(" + 推荐点击坐标.x + ", " + 推荐点击坐标.y + ");");
                console.log("    🎯 选择依据: 面积最大的轮廓 (面积:" + 有效轮廓信息列表[0].面积 + ")");
            }

            // 9. 释放OpenCV资源
            原始Mat.release();
            灰度Mat.release();
            模糊Mat.release();
            边缘Mat.release();
            层次结构.release();
            for (var j = 0; j < 轮廓列表.size(); j++) {
                轮廓列表.get(j).release();
            }

            return {
                轮廓信息: 有效轮廓信息列表,
                边缘轮廓信息: 边缘轮廓信息列表,
                推荐点击坐标: 推荐点击坐标
            };

        } catch (e) {
            console.error("  ❌ OpenCV边缘检测轮廓分析失败: " + e);
            return null;
        }

    } else {
        // ==================== 接口1：完整检测 ====================
        // 看广告(区域名称, 选项) 或 看广告(区域名称)
        var 选项 = 选项或坐标或灰度Mat || {};

        // 兼容旧版本调用方式：看广告(区域名称, 保存调试图片, 检测配置)
        if (typeof 选项 === 'boolean') {
            选项 = {
                保存调试图片: 选项,
                检测配置: 选项或区域名称 || {}
            };
        }

        // 从选项中提取参数
        var 保存调试图片参数 = 选项.保存调试图片 !== false;
        var 检测配置参数 = 选项.检测配置 || 选项;

        console.log("🔍 开始看广告检测...");

        // 默认参数设置
        保存调试图片参数 = 保存调试图片参数 !== false;  // 默认保存调试图片
        检测配置参数 = 检测配置参数 || {};

        // 检查屏幕捕获权限
        if (!images.requestScreenCapture()) {
            console.log("❌ 屏幕捕获权限获取失败");
            return null;
        }

        try {
            // 等待一段时间确保界面稳定
            var 等待时间 = 检测配置参数.等待时间 || 5000;
            console.log("⏰ 等待" + (等待时间/1000) + "秒后开始检测...");
            sleep(等待时间);

            console.log("🚀 开始执行检测...");

            // 📸 捕获屏幕画面
            console.log("📸 正在捕获屏幕画面...");
            var 屏幕图像 = images.captureScreen();
            if (!屏幕图像) {
                console.log("❌ 屏幕截图失败");
                return null;
            }
            console.log("✅ 屏幕截图成功");

            // 🎯 定义检测区域配置
            var 区域配置列表 = [
                {
                    名称: "右广告",
                    坐标: [425, 2, 110, 176],  // [x, y, width, height]
                    描述: "右侧广告区域，通常包含播放按钮、>>号等"
                },
                {
                    名称: "左广告",
                    坐标: [5, 2, 110, 176],
                    描述: "左侧广告区域"
                },
                {
                    名称: "close区域",
                    坐标: [480, 50, 60, 60],
                    描述: "关闭按钮区域，通常包含X号"
                }
            ];

            // 🔍 根据指定区域过滤检测范围
            var 待检测区域列表 = [];
            if (区域名称或图像或Mat) {
                for (var i = 0; i < 区域配置列表.length; i++) {
                    if (区域配置列表[i].名称 === 区域名称或图像或Mat) {
                        待检测区域列表.push(区域配置列表[i]);
                        break;
                    }
                }
                if (待检测区域列表.length === 0) {
                    console.log("⚠️ 未找到指定区域: " + 区域名称或图像或Mat);
                    return null;
                }
            } else {
                待检测区域列表 = 区域配置列表;
            }

            var 检测结果列表 = [];

            // 🎯 对每个区域进行边缘检测分析
            for (var j = 0; j < 待检测区域列表.length; j++) {
                var 区域配置 = 待检测区域列表[j];
                var 区域坐标 = 区域配置.坐标;

                console.log("📋 检测区域: " + 区域配置.名称);
                console.log("  📍 区域坐标: (" + 区域坐标[0] + ", " + 区域坐标[1] + ") 尺寸: " + 区域坐标[2] + "×" + 区域坐标[3]);

                // 🔪 裁剪区域图像
                var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);
                if (!区域图像) {
                    console.log("  ❌ 区域图像裁剪失败");
                    continue;
                }

                // 🔍 调用边缘检测接口
                var 边缘检测结果 = 看广告(区域图像, 区域坐标, {
                    区域名称: 区域配置.名称,
                    保存调试图片: 保存调试图片参数,
                    检测配置: 检测配置参数
                });

                if (边缘检测结果 && 边缘检测结果.轮廓信息.length > 0) {
                    检测结果列表.push({
                        区域名称: 区域配置.名称,
                        区域坐标: 区域坐标,
                        轮廓信息: 边缘检测结果.轮廓信息,
                        边缘轮廓信息: 边缘检测结果.边缘轮廓信息,
                        推荐点击坐标: 边缘检测结果.推荐点击坐标
                    });

                    console.log("✅ " + 区域配置.名称 + " 检测完成，发现 " + 边缘检测结果.轮廓信息.length + " 个轮廓");
                } else {
                    console.log("ℹ️ " + 区域配置.名称 + " 未检测到有效轮廓");
                }

                // 释放区域图像资源
                区域图像.recycle();
            }

            // 释放屏幕图像资源
            屏幕图像.recycle();

            // 📊 输出检测结果汇总
            if (检测结果列表.length > 0) {
                console.log("🎉 检测完成！共发现 " + 检测结果列表.length + " 个有效区域");

                // 🎯 输出最佳推荐点击坐标
                var 最佳结果 = 检测结果列表[0];  // 取第一个有效结果
                if (最佳结果.推荐点击坐标) {
                    console.log("🖱️ 最佳推荐点击坐标: (" + 最佳结果.推荐点击坐标.x + ", " + 最佳结果.推荐点击坐标.y + ")");
                    console.log("📝 AutoXjs点击代码: click(" + 最佳结果.推荐点击坐标.x + ", " + 最佳结果.推荐点击坐标.y + ");");
                }

                return {
                    成功: true,
                    检测结果: 检测结果列表,
                    推荐点击坐标: 最佳结果.推荐点击坐标
                };
            } else {
                console.log("ℹ️ 未在任何区域检测到有效轮廓");
                return {
                    成功: false,
                    消息: "未检测到有效轮廓"
                };
            }

        } catch (e) {
            console.error("❌ 看广告检测失败: " + e);
            return {
                成功: false,
                错误: e.toString()
            };
        }
    }

    // ==================== 接口4：图片匹配测试功能 ====================
    function 图片匹配测试功能(匹配选项) {
        console.log("🔍 开始图片匹配测试...");
        匹配选项 = 匹配选项 || {};

        try {
            var 匹配区域 = 匹配选项.匹配区域 || "右广告";

            // 首先进行轮廓检测，获取保存的边缘检测图片
            console.log("🔧 先进行轮廓检测，获取边缘检测图片...");
            var 轮廓检测结果 = 看广告(匹配区域, {
                保存调试图片: true,
                检测配置: {
                    等待时间: 1000
                }
            });

            if (!轮廓检测结果 || !轮廓检测结果.成功) {
                console.log("❌ 轮廓检测失败，无法获取边缘检测图片");
                return {成功: false, 错误: "轮廓检测失败"};
            }

            // 直接使用固定的边缘检测图片文件名
            var 图片目录 = "/storage/emulated/0/Pictures/";
            var 边缘图片文件名 = 匹配区域 + "_边缘检测.jpg";
            var 边缘图片路径 = 图片目录 + 边缘图片文件名;

            // 检查文件是否存在
            if (!files.exists(边缘图片路径)) {
                console.log("❌ 未找到边缘检测图片文件: " + 边缘图片文件名);
                return {成功: false, 错误: "未找到边缘检测图片文件: " + 边缘图片文件名};
            }

            console.log("📸 使用边缘检测图片: " + 边缘图片文件名);

            // 读取边缘检测图片
            var 边缘检测图片 = images.read(边缘图片路径);
            if (!边缘检测图片) {
                console.log("❌ 无法读取边缘检测图片: " + 边缘图片路径);
                return {成功: false, 错误: "无法读取边缘检测图片"};
            }

            // 设置匹配选项
            var 最终匹配选项 = {
                相似度阈值: 匹配选项.相似度阈值 || 0.7,
                匹配区域: 匹配区域,
                模板目录: 匹配选项.模板目录
            };

            // 使用边缘检测图片进行匹配
            var 匹配结果 = 图片匹配(边缘检测图片, 最终匹配选项);

            if (匹配结果 && 匹配结果.成功) {
                console.log("✅ 图片匹配测试成功！找到 " + 匹配结果.匹配列表.length + " 个匹配");
                if (匹配结果.最佳匹配) {
                    console.log("🎯 最佳匹配: " + 匹配结果.最佳匹配.模板文件);
                }
            } else {
                console.log("ℹ️ 图片匹配测试完成，未找到匹配项");
            }

            // 释放边缘检测图片资源
            try {
                边缘检测图片.recycle();
            } catch (e) {
                // 忽略回收错误
            }

            return 匹配结果;

        } catch (e) {
            console.error("❌ 图片匹配测试失败: " + e);
            console.log("💡 建议：确保已获取截图权限，且模板文件存在");
            return {成功: false, 错误: e.toString()};
        }
    }
}

// ==================== 模板预处理功能 ====================
function 模板预处理(广告目录路径, 处理选项) {
        console.log("🔧 开始模板预处理...");
        处理选项 = 处理选项 || {};

        var 基础路径 = 广告目录路径 || "/storage/emulated/0/脚本/magic/assets/算数游戏/广告";
        var 处理结果 = {
            成功: true,
            处理统计: {},
            错误列表: []
        };

        try {
            // 获取广告目录列表
            var 广告目录列表 = ["右广告", "左广告", "close区域"];

            for (var i = 0; i < 广告目录列表.length; i++) {
                var 目录名 = 广告目录列表[i];
                var 目录路径 = 基础路径 + "/" + 目录名;

                console.log("📁 处理目录: " + 目录名);

                // 检查目录是否存在
                if (!files.exists(目录路径)) {
                    console.log("  ⚠️ 目录不存在: " + 目录路径);
                    continue;
                }

                // 获取目录下的图片文件
                var 图片文件列表 = files.listDir(目录路径, function(name) {
                    return name.endsWith(".png") || name.endsWith(".jpg") || name.endsWith(".jpeg");
                });

                处理结果.处理统计[目录名] = {
                    原始图片数: 图片文件列表.length,
                    处理成功数: 0,
                    处理失败数: 0
                };

                console.log("  📊 发现 " + 图片文件列表.length + " 个图片文件");

                // 处理每个图片文件
                for (var j = 0; j < 图片文件列表.length; j++) {
                    var 图片文件名 = 图片文件列表[j];
                    var 图片路径 = 目录路径 + "/" + 图片文件名;

                    try {
                        console.log("    🖼️ 处理图片: " + 图片文件名);

                        // 读取原始图片
                        var 原始图片 = images.read(图片路径);
                        if (!原始图片) {
                            throw new Error("无法读取图片: " + 图片路径);
                        }

                        // 进行边缘检测处理
                        var 边缘处理结果 = 处理图片边缘(原始图片, 处理选项);

                        if (边缘处理结果.成功) {
                            // 直接覆盖原图片
                            var 输出路径 = 图片路径;  // 使用原图路径，直接覆盖

                            // 使用Imgcodecs直接保存Mat，覆盖原图
                            Imgcodecs.imwrite(输出路径, 边缘处理结果.边缘Mat);
                            console.log("      ✅ 覆盖原图为边缘图: " + 图片文件名);

                            处理结果.处理统计[目录名].处理成功数++;

                            // 释放Mat资源
                            边缘处理结果.边缘Mat.release();
                        } else {
                            throw new Error("边缘处理失败: " + 边缘处理结果.错误);
                        }

                        // 释放原始图片资源
                        原始图片.recycle();

                    } catch (e) {
                        console.error("    ❌ 处理图片失败: " + 图片文件名 + " - " + e);
                        处理结果.处理统计[目录名].处理失败数++;
                        处理结果.错误列表.push({
                            目录: 目录名,
                            文件: 图片文件名,
                            错误: e.toString()
                        });
                    }
                }
            }

            // 输出处理统计
            console.log("📊 模板预处理完成统计:");
            for (var 目录 in 处理结果.处理统计) {
                var 统计 = 处理结果.处理统计[目录];
                console.log("  " + 目录 + ": 原始=" + 统计.原始图片数 + " 成功=" + 统计.处理成功数 + " 失败=" + 统计.处理失败数);
            }

            return 处理结果;

        } catch (e) {
            console.error("❌ 模板预处理失败: " + e);
            处理结果.成功 = false;
            处理结果.错误 = e.toString();
            return 处理结果;
        }
    }

    // 处理单个图片的边缘检测
    function 处理图片边缘(原始图片, 选项) {
        try {
            // 转换为OpenCV Mat格式
            var 原始Mat = 原始图片.getMat();

            // 转换为灰度图
            var 灰度Mat = new Mat();
            Imgproc.cvtColor(原始Mat, 灰度Mat, Imgproc.COLOR_BGR2GRAY);

            // 高斯模糊
            var 模糊Mat = new Mat();
            var 模糊核心 = 选项.高斯模糊核心 || 3;
            Imgproc.GaussianBlur(灰度Mat, 模糊Mat, new Size(模糊核心, 模糊核心), 0);

            // Canny边缘检测
            var 边缘Mat = new Mat();
            var Canny低阈值 = 选项.Canny低阈值 || 50;
            var Canny高阈值 = 选项.Canny高阈值 || 150;
            Imgproc.Canny(模糊Mat, 边缘Mat, Canny低阈值, Canny高阈值);

            // 释放Mat资源
            原始Mat.release();
            灰度Mat.release();
            模糊Mat.release();

            return {
                成功: true,
                边缘Mat: 边缘Mat
            };

        } catch (e) {
            return {
                成功: false,
                错误: e.toString()
            };
        }
    }

// ==================== 图片匹配功能 ====================
function 图片匹配(目标图像, 匹配选项) {
        console.log("🔍 开始图片匹配...");
        匹配选项 = 匹配选项 || {};

        // 🎯 相似度阈值设置 - 参考看广告.js的设计
        // AutoXjs images.findImage的threshold: 0~1，默认0.9，越高越严格
        // 0.7 = 70%相似度，适中的匹配要求
        var 相似度阈值 = 匹配选项.相似度阈值 || 0.7;
        var 匹配区域 = 匹配选项.匹配区域 || "全部";
        var 模板目录 = 匹配选项.模板目录 || "/storage/emulated/0/脚本/magic/assets/算数游戏/广告";

        var 匹配结果 = {
            成功: false,
            匹配列表: [],
            最佳匹配: null
        };

        try {
            // 🔧 创建目标图像的副本，避免重复运行时图像被重复处理
            console.log("🔧 创建边缘检测图像副本进行匹配...");
            var 处理后的目标图像 = images.copy(目标图像);

            // 获取匹配区域的全屏坐标偏移（用于坐标转换）
            var 区域配置列表 = [
                {
                    名称: "右广告",
                    坐标: [425, 2, 110, 176]  // [x, y, width, height]
                },
                {
                    名称: "左广告",
                    坐标: [5, 2, 110, 176]
                },
                {
                    名称: "close区域",
                    坐标: [480, 50, 60, 60]
                }
            ];

            var 区域偏移坐标 = null;
            for (var i = 0; i < 区域配置列表.length; i++) {
                if (区域配置列表[i].名称 === 匹配区域) {
                    区域偏移坐标 = 区域配置列表[i].坐标;
                    break;
                }
            }

            if (!区域偏移坐标) {
                console.log("⚠️ 未找到匹配区域的坐标配置: " + 匹配区域);
                区域偏移坐标 = [0, 0, 0, 0]; // 默认无偏移
            }

            console.log("📍 区域偏移坐标: (" + 区域偏移坐标[0] + ", " + 区域偏移坐标[1] + ")");

            // 根据匹配区域确定要搜索的目录
            var 搜索目录列表 = [];
            if (匹配区域 === "全部") {
                搜索目录列表 = ["右广告", "左广告", "close区域"];
            } else {
                搜索目录列表 = [匹配区域];
            }

            for (var i = 0; i < 搜索目录列表.length; i++) {
                var 目录名 = 搜索目录列表[i];
                var 目录路径 = 模板目录 + "/" + 目录名;

                if (!files.exists(目录路径)) {
                    continue;
                }

                // 获取模板图片（已经是边缘处理后的原图）
                var 模板文件列表 = files.listDir(目录路径, function(name) {
                    return name.endsWith(".png") || name.endsWith(".jpg") || name.endsWith(".jpeg");
                });

                console.log("📁 搜索目录: " + 目录名 + " (模板数: " + 模板文件列表.length + ")");

                for (var j = 0; j < 模板文件列表.length; j++) {
                    var 模板文件名 = 模板文件列表[j];
                    var 模板路径 = 目录路径 + "/" + 模板文件名;

                    try {
                        // 读取模板图片
                        var 模板图片 = images.read(模板路径);
                        if (!模板图片) {
                            console.log("  ⚠️ 无法读取模板: " + 模板文件名);
                            continue;
                        }

                        console.log("  🔍 开始匹配模板: " + 模板文件名);

                        // 关键修复：先获取尺寸，再进行匹配，避免在匹配过程中图片被回收
                        var 模板宽度 = 0, 模板高度 = 0;
                        try {
                            模板宽度 = 模板图片.getWidth();
                            模板高度 = 模板图片.getHeight();
                        } catch (sizeError) {
                            console.log("  ⚠️ 无法获取模板尺寸: " + sizeError);
                            try { 模板图片.recycle(); } catch (e) {}
                            continue;
                        }

                        // 执行图片匹配 - 使用matchTemplate获取真实相似度
                        var 匹配点 = null;
                        var 真实相似度 = 0;
                        try {
                            // 🔍 使用matchTemplate进行匹配（返回详细匹配信息包含相似度）
                            var 模板匹配结果 = images.matchTemplate(处理后的目标图像, 模板图片, {
                                threshold: 相似度阈值,
                                region: 匹配选项.搜索区域,
                                max: 1  // 只需要最佳匹配
                            });

                            // 🎯 检查匹配结果并获取真实相似度
                            if (模板匹配结果 && 模板匹配结果.matches && 模板匹配结果.matches.length > 0) {
                                var 最佳匹配 = 模板匹配结果.matches[0];
                                匹配点 = 最佳匹配.point;
                                真实相似度 = 最佳匹配.similarity;
                                console.log("  📊 匹配成功，真实相似度: " + 真实相似度.toFixed(3));
                            } else {
                                console.log("  ❌ 未找到匹配，相似度低于阈值 " + 相似度阈值);
                            }
                        } catch (findError) {
                            console.log("  ⚠️ 图片匹配失败: " + 模板文件名 + " - " + findError);
                        }

                        // 立即释放模板图片，避免后续操作时图片被意外回收
                        try {
                            模板图片.recycle();
                        } catch (recycleError) {
                            // 忽略回收错误
                        }

                        // 处理匹配结果
                        if (匹配点) {
                            // 🎯 计算模板中心点坐标（从左上角转换为中心点）
                            var 相对中心点坐标 = {
                                x: Math.round(匹配点.x + 模板宽度 / 2),
                                y: Math.round(匹配点.y + 模板高度 / 2)
                            };

                            // 🌍 计算全屏中心点坐标
                            var 全屏中心点坐标 = {
                                x: 相对中心点坐标.x + 区域偏移坐标[0],
                                y: 相对中心点坐标.y + 区域偏移坐标[1]
                            };

                            var 匹配信息 = {
                                模板文件: 模板文件名,
                                目录: 目录名,
                                原始匹配点: 匹配点,         // 原始左上角坐标
                                相对坐标: 相对中心点坐标,   // 相对于边缘检测图片的中心点坐标
                                全屏坐标: 全屏中心点坐标,   // 转换后的全屏中心点坐标
                                匹配点: 全屏中心点坐标,     // 保持兼容性，默认返回全屏中心点坐标
                                相似度: 真实相似度,         // 使用matchTemplate获取的真实相似度
                                模板尺寸: {
                                    宽度: 模板宽度,
                                    高度: 模板高度
                                },
                                区域偏移: {
                                    x: 区域偏移坐标[0],
                                    y: 区域偏移坐标[1]
                                }
                            };

                            匹配结果.匹配列表.push(匹配信息);
                            console.log("  ✅ 找到匹配: " + 模板文件名 +
                                       " 相似度: " + 真实相似度.toFixed(3) +
                                       " 左上角: (" + 匹配点.x + ", " + 匹配点.y + ")" +
                                       " 中心点: (" + 相对中心点坐标.x + ", " + 相对中心点坐标.y + ")" +
                                       " 全屏中心: (" + 全屏中心点坐标.x + ", " + 全屏中心点坐标.y + ")");
                        }

                    } catch (e) {
                        console.error("  ❌ 匹配模板失败: " + 模板文件名 + " - " + e);
                    }
                }
            }

            // 选择最佳匹配（按真实相似度排序，相似度高的优先）
            if (匹配结果.匹配列表.length > 0) {
                匹配结果.匹配列表.sort(function(a, b) {
                    return b.相似度 - a.相似度; // 相似度高的排在前面
                });
                匹配结果.最佳匹配 = 匹配结果.匹配列表[0];
                匹配结果.成功 = true;

                console.log("🎯 最佳匹配: " + 匹配结果.最佳匹配.模板文件 +
                           " 相似度: " + 匹配结果.最佳匹配.相似度.toFixed(3) +
                           " 全屏中心点: (" + 匹配结果.最佳匹配.全屏坐标.x + ", " + 匹配结果.最佳匹配.全屏坐标.y + ")" +
                           " 模板尺寸: " + 匹配结果.最佳匹配.模板尺寸.宽度 + "×" + 匹配结果.最佳匹配.模板尺寸.高度);
            } else {
                console.log("⚠️ 未找到匹配的模板");
            }

            return 匹配结果;

        } catch (e) {
            console.error("❌ 图片匹配失败: " + e);
            匹配结果.错误 = e.toString();
            return 匹配结果;
        }
}

// ==================== 辅助函数：处理图像为边缘格式 ====================
function 处理图像为边缘格式(原始图像, 选项) {
    try {
        选项 = 选项 || {};

        // 转换为OpenCV Mat格式
        var 原始Mat = 原始图像.getMat();

        // 转换为灰度图
        var 灰度Mat = new Mat();
        Imgproc.cvtColor(原始Mat, 灰度Mat, Imgproc.COLOR_BGR2GRAY);

        // 高斯模糊
        var 模糊Mat = new Mat();
        var 模糊核心 = 选项.高斯模糊核心 || 3;
        Imgproc.GaussianBlur(灰度Mat, 模糊Mat, new Size(模糊核心, 模糊核心), 0);

        // Canny边缘检测
        var 边缘Mat = new Mat();
        var Canny低阈值 = 选项.Canny低阈值 || 50;
        var Canny高阈值 = 选项.Canny高阈值 || 150;
        Imgproc.Canny(模糊Mat, 边缘Mat, Canny低阈值, Canny高阈值);

        // 转换回Image对象 - 使用临时文件方式避免回收问题
        var 临时路径 = "/storage/emulated/0/Pictures/temp_edge_" + Date.now() + ".png";
        Imgcodecs.imwrite(临时路径, 边缘Mat);
        var 边缘图片 = images.read(临时路径);

        // 删除临时文件
        try {
            files.remove(临时路径);
        } catch (e) {
            // 忽略删除错误
        }

        // 释放Mat资源
        原始Mat.release();
        灰度Mat.release();
        模糊Mat.release();
        边缘Mat.release();

        return 边缘图片;

    } catch (e) {
        console.error("处理图像为边缘格式失败: " + e);
        return null;
    }
}



// // 🚀 模块导出（供其他脚本调用）
// module.exports = {
//     看广告: 看广告,
//     模板预处理: 模板预处理,
//     图片匹配: 图片匹配
// };

// 🧪 测试代码示例（可选）
if (typeof module === 'undefined') {
    console.log("🎯 开始测试观看广告模块...");

    // 测试1：模板预处理
    console.log("\n=== 测试1：模板预处理 ===");
    var 预处理结果 = 模板预处理(null, {
        Canny低阈值: 50,
        Canny高阈值: 150,
        高斯模糊核心: 3
    });

    if (预处理结果 && 预处理结果.成功) {
        console.log("✅ 模板预处理测试成功！");
    } else {
        console.log("❌ 模板预处理测试失败: " + (预处理结果 ? 预处理结果.错误 : "未知错误"));
    }

    // 测试2：完整检测（包含轮廓特征分析）
    console.log("\n=== 测试2：完整检测 ===");
    var 检测结果 = 看广告("右广告", {
        保存调试图片: true,
        检测配置: {
            等待时间: 3000
        }
    });

    if (检测结果 && 检测结果.成功) {
        console.log("✅ 完整检测测试成功！推荐点击坐标: (" + 检测结果.推荐点击坐标.x + ", " + 检测结果.推荐点击坐标.y + ")");

        // 测试3：图片匹配（集成到看广告函数中）
        console.log("\n=== 测试3：图片匹配 ===");
        var 匹配测试结果 = 看广告("图片匹配", {
            相似度阈值: 0.7,
            匹配区域: "右广告"
        });

        if (匹配测试结果 && 匹配测试结果.成功) {
            console.log("✅ 图片匹配集成测试成功！");
        } else {
            console.log("ℹ️ 图片匹配集成测试完成，结果: " + (匹配测试结果 ? 匹配测试结果.错误 : "未知错误"));
        }

        console.log("📊 轮廓特征分析已集成到边缘检测输出中");
    } else {
        console.log("ℹ️ 完整检测测试完成，结果: " + (检测结果 ? 检测结果.消息 : "未知错误"));
    }
}
