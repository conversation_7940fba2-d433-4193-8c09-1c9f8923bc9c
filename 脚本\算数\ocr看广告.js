/**
 * Paddle OCR 看广告识别模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：全屏OCR符号识别，专门识别X号、播放按钮、各种符号
 * 作者：Magic项目组
 * 版本：v3.0
 */

/**
 * 看广告 - 使用Paddle OCR进行全屏符号识别
 * @param {Object} 选项 - 识别选项（可选）
 * @returns {Object} 识别结果包含符号信息、坐标和置信度
 *
 * 调用方式：
 * 1. 看广告()                           - 使用默认配置全屏符号识别
 * 2. 看广告({置信度阈值: 0.8})           - 自定义置信度阈值
 * 3. 看广告({符号类型: "播放"})          - 只识别特定类型符号
 */
function 看广告(选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(选项);

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        traceLog("开始全屏OCR识别，配置: " + JSON.stringify(配置));

        // 使用Paddle OCR进行全屏识别
        var OCR结果 = paddle.ocr(屏幕图像, 配置.CPU核心数, 配置.使用快速模型);

        // 处理识别结果
        var 处理结果 = 处理OCR结果(OCR结果, 配置);

        // 释放资源
        屏幕图像.recycle();

        return {
            成功: true,
            识别结果: 处理结果,
            消息: "全屏OCR识别完成"
        };

    } catch (e) {
        console.error("看广告OCR识别失败:", e);
        traceLog("OCR识别错误详情: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "OCR识别失败"
        };
    }
}

/**
 * 解析函数参数
 */
function 解析参数(选项) {
    var 默认配置 = {
        CPU核心数: 2,                    // 根据要求改为2
        使用快速模型: false,             // true=快速模型, false=精准模型
        置信度阈值: 0.7,                // 符号识别置信度阈值
        符号类型: "全部",               // 识别的符号类型：全部、播放、关闭、数字、箭头
        符号过滤: true                  // 是否启用符号过滤
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理选项参数
    if (选项 && typeof 选项 === "object") {
        Object.assign(配置, 选项);
    }

    return 配置;
}

/**
 * 处理OCR识别结果 - 专门用于符号识别
 */
function 处理OCR结果(OCR结果, 配置) {
    if (!OCR结果 || OCR结果.length === 0) {
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: {},
            平均置信度: 0,
            完整文本: "",
            消息: "未识别到符号"
        };
    }

    var 符号列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 符号分类 = {
        播放符号: [],
        关闭符号: [],
        数字符号: [],
        符号: [],
        文字: []
    };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];

        // 置信度过滤
        if (项目.confidence >= 配置.置信度阈值) {
            var 识别文字 = 项目.text;

            // 符号过滤和分类
            var 符号信息 = 分析符号类型(识别文字, 项目.confidence);

            if (符号信息.是符号 && (配置.符号类型 === "全部" || 符号信息.类型 === 配置.符号类型)) {
                符号列表.push(识别文字);
                置信度列表.push(项目.confidence);

                // 计算中心坐标
                var 中心坐标 = {
                    left: 项目.bounds.left,
                    top: 项目.bounds.top,
                    right: 项目.bounds.right,
                    bottom: 项目.bounds.bottom,
                    centerX: Math.round((项目.bounds.left + 项目.bounds.right) / 2),
                    centerY: Math.round((项目.bounds.top + 项目.bounds.bottom) / 2),
                    width: 项目.bounds.right - 项目.bounds.left,
                    height: 项目.bounds.bottom - 项目.bounds.top
                };
                坐标列表.push(中心坐标);

                // 符号分类
                var 符号项 = {
                    符号: 识别文字,
                    类型: 符号信息.类型,
                    描述: 符号信息.描述,
                    置信度: 项目.confidence,
                    坐标: 中心坐标
                };

                switch (符号信息.类型) {
                    case "播放":
                        符号分类.播放符号.push(符号项);
                        break;
                    case "关闭":
                        符号分类.关闭符号.push(符号项);
                        break;
                    case "数字":
                        符号分类.数字符号.push(符号项);
                        break;
                    case "符号":
                        符号分类.符号.push(符号项);
                        break;
                    default:
                        符号分类.文字.push(符号项);
                }

                总置信度 += 项目.confidence;
                有效符号数量++;
            }
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 符号列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        符号分类: 符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 符号列表.join(" "),
        原始OCR结果: OCR结果
    };
}

/**
 * 分析符号类型 - 通用符号识别
 */
function 分析符号类型(文字, 置信度) {
    // 基于文字内容的智能分析，不依赖预定义符号列表

    // 检查是否包含播放相关关键词
    if (文字.toLowerCase().indexOf("play") !== -1 ||
        文字.indexOf("播放") !== -1 ||
        文字.indexOf(">>") !== -1) {
        return {
            是符号: true,
            类型: "播放",
            描述: "播放相关符号或文字",
            匹配符号: 文字
        };
    }

    // 检查是否包含关闭相关关键词
    if (文字.toLowerCase().indexOf("close") !== -1 ||
        文字.toLowerCase().indexOf("x") !== -1 ||
        文字.indexOf("关闭") !== -1 ||
        文字.indexOf("跳过") !== -1) {
        return {
            是符号: true,
            类型: "关闭",
            描述: "关闭相关符号或文字",
            匹配符号: 文字
        };
    }

    // 检查是否为纯数字
    if (/^[0-9０-９]+$/.test(文字)) {
        return {
            是符号: true,
            类型: "数字",
            描述: "数字符号",
            匹配符号: 文字
        };
    }

    // 检查是否为短符号（长度<=3且不包含中文字母）
    if (文字.length <= 3 && /^[^\u4e00-\u9fa5a-zA-Z]*$/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "短符号或特殊字符",
            匹配符号: 文字
        };
    }

    // 检查是否为单个字符且不是常见汉字或字母
    if (文字.length === 1 && !/^[a-zA-Z\u4e00-\u9fa5]$/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "单个特殊符号",
            匹配符号: 文字
        };
    }

    // 检查是否包含特殊字符（非字母数字汉字）
    if (/[^\u4e00-\u9fa5a-zA-Z0-9\s]/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含特殊符号的文字",
            匹配符号: 文字
        };
    }

    return {
        是符号: false,
        类型: "文字",
        描述: "普通文字",
        匹配符号: null
    };
}



// // 导出模块
// module.exports = {
//     看广告: 看广告
// };

// 测试代码 - 直接运行
console.log("🎯 开始测试Paddle OCR精准符号识别功能...");

// 精准符号识别测试
console.log("\n=== 精准符号识别测试 ===");
var 结果 = 看广告();

if (结果.成功) {
    console.log("✅ 符号识别成功！");
    console.log("📊 识别到 " + 结果.识别结果.符号数量 + " 个符号");
    console.log("📝 完整符号: " + 结果.识别结果.完整文本);
    console.log("🎯 平均置信度: " + 结果.识别结果.平均置信度);

    // 输出符号分类统计
    var 分类 = 结果.识别结果.符号分类;
    console.log("\n📋 符号分类统计:");
    console.log("  🎮 播放符号: " + 分类.播放符号.length + " 个");
    console.log("  ❌ 关闭符号: " + 分类.关闭符号.length + " 个");
    console.log("  🔢 数字符号: " + 分类.数字符号.length + " 个");
    console.log("  � 通用符号: " + 分类.符号.length + " 个");
    console.log("  📝 文字内容: " + 分类.文字.length + " 个");

    // 输出详细符号信息
    console.log("\n📋 详细符号信息:");

    // 播放符号
    if (分类.播放符号.length > 0) {
        console.log("🎮 播放符号:");
        for (var i = 0; i < 分类.播放符号.length; i++) {
            var 符号 = 分类.播放符号[i];
            console.log("  [" + (i + 1) + "] 符号: \"" + 符号.符号 + "\" (" + 符号.描述 + ")");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("");
        }
    }

    // 关闭符号
    if (分类.关闭符号.length > 0) {
        console.log("❌ 关闭符号:");
        for (var i = 0; i < 分类.关闭符号.length; i++) {
            var 符号 = 分类.关闭符号[i];
            console.log("  [" + (i + 1) + "] 符号: \"" + 符号.符号 + "\" (" + 符号.描述 + ")");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("");
        }
    }

    // 通用符号
    if (分类.符号.length > 0) {
        console.log("🔣 通用符号:");
        for (var i = 0; i < Math.min(5, 分类.符号.length); i++) {
            var 符号 = 分类.符号[i];
            console.log("  [" + (i + 1) + "] 符号: \"" + 符号.符号 + "\" (" + 符号.描述 + ")");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("");
        }
        if (分类.符号.length > 5) {
            console.log("  ... 还有 " + (分类.符号.length - 5) + " 个通用符号");
        }
    }

    // 数字符号
    if (分类.数字符号.length > 0) {
        console.log("🔢 数字符号:");
        for (var i = 0; i < Math.min(3, 分类.数字符号.length); i++) {
            var 符号 = 分类.数字符号[i];
            console.log("  [" + (i + 1) + "] 符号: \"" + 符号.符号 + "\" (" + 符号.描述 + ")");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("");
        }
        if (分类.数字符号.length > 3) {
            console.log("  ... 还有 " + (分类.数字符号.length - 3) + " 个数字符号");
        }
    }

    // 文字内容
    if (分类.文字.length > 0) {
        console.log("📝 文字内容:");
        for (var i = 0; i < Math.min(3, 分类.文字.length); i++) {
            var 符号 = 分类.文字[i];
            console.log("  [" + (i + 1) + "] 文字: \"" + 符号.符号 + "\" (" + 符号.描述 + ")");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("");
        }
        if (分类.文字.length > 3) {
            console.log("  ... 还有 " + (分类.文字.length - 3) + " 个文字内容");
        }
    }

} else {
    console.log("❌ 符号识别失败: " + 结果.错误);
}

console.log("📊 Paddle OCR精准符号识别功能测试完成");