/**
 * Paddle OCR 符号识别模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：全屏OCR符号识别，专门识别符号，过滤文字
 * 作者：Magic项目组
 * 版本：v4.0
 */

/**
 * 看广告 - 使用Paddle OCR进行全屏符号识别
 * @param {Object} 选项 - 识别选项（可选）
 * @returns {Object} 识别结果包含符号信息、坐标和置信度
 *
 * 调用方式：
 * 1. 看广告()                           - 使用默认配置全屏符号识别
 * 2. 看广告({置信度阈值: 0.1})           - 自定义置信度阈值
 */
function 看广告(选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(选项);

        // 延迟5秒后再识别
        console.log("⏰ 等待5秒后开始识别...");
        sleep(5000);
        console.log("📸 开始截图识别");

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        traceLog("开始全屏OCR符号识别，配置: " + JSON.stringify(配置));

        var 处理结果;

        // 三重识别策略：原图识别 + 二值化识别 + 反向二值化识别
        处理结果 = 三重OCR识别(屏幕图像, 配置);

        // 释放资源
        屏幕图像.recycle();

        return {
            成功: true,
            识别结果: 处理结果,
            消息: "全屏OCR符号识别完成"
        };

    } catch (e) {
        console.error("看广告OCR识别失败:", e);
        traceLog("OCR识别错误详情: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "OCR识别失败"
        };
    }
}

/**
 * 解析函数参数
 */
function 解析参数(选项) {
    var 默认配置 = {
        CPU核心数: 2,                    // 根据要求改为2
        使用快速模型: false,             // true=快速模型, false=精准模型
        置信度阈值: 0.1,                // 降低置信度阈值，识别更多内容
        启用二值化: true,               // 是否启用二值化处理
        二值化阈值: 186,                // 二值化阈值 (186-255范围)
        二值化最大值: 255,              // 二值化最大值
        反向二值化阈值: 241,            // 反向二值化阈值 (241-255范围)
        反向二值化最大值: 255,          // 反向二值化最大值（大于阈值为0，其他为255）
        启用图片匹配: true,             // 是否启用图片模板匹配
        图片匹配阈值: 0.8,              // 图片匹配相似度阈值 (0-1)
        图片模板路径: "../../assets/算数游戏/广告/右广告"  // 图片模板相对路径
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理选项参数
    if (选项 && typeof 选项 === "object") {
        Object.assign(配置, 选项);
    }

    return 配置;
}

/**
 * 图像二值化处理 - 提高OCR识别精度
 */
function 图像二值化处理(原图像, 配置) {
    try {
        if (!配置.启用二值化) {
            console.log("📸 跳过二值化处理");
            return 原图像;
        }

        console.log("📸 开始二值化处理，阈值: " + 配置.二值化阈值 + ", 最大值: " + 配置.二值化最大值);

        // 先转换为灰度图像
        var 灰度图像 = images.grayscale(原图像);

        // 进行二值化处理
        var 二值化图像 = images.threshold(灰度图像, 配置.二值化阈值, 配置.二值化最大值, "BINARY");

        // 释放中间图像资源
        灰度图像.recycle();

        console.log("✅ 二值化处理完成");
        return 二值化图像;

    } catch (e) {
        console.error("二值化处理失败:", e);
        console.log("⚠️ 使用原图像进行OCR识别");
        return 原图像;
    }
}

/**
 * 反向二值化处理 - 大于阈值为0，其他为最大值
 */
function 反向二值化处理(原图像, 配置) {
    try {
        console.log("📸 开始反向二值化处理，阈值: " + 配置.反向二值化阈值 + ", 最大值: " + 配置.反向二值化最大值);

        // 先转换为灰度图像
        var 灰度图像 = images.grayscale(原图像);

        // 进行反向二值化处理 (BINARY_INV: 大于阈值为0，其他为最大值)
        var 反向二值化图像 = images.threshold(灰度图像, 配置.反向二值化阈值, 配置.反向二值化最大值, "BINARY_INV");

        // 释放中间图像资源
        灰度图像.recycle();

        console.log("✅ 反向二值化处理完成");
        return 反向二值化图像;

    } catch (e) {
        console.error("反向二值化处理失败:", e);
        console.log("⚠️ 使用原图像进行OCR识别");
        return 原图像;
    }
}

/**
 * 双重OCR识别策略 - 升级为三重识别：原图识别 + 二值化识别 + 反向二值化识别
 * 无论识别结果数量如何，都会执行完整的三重识别流程
 */
function 三重OCR识别(屏幕图像, 配置) {
    console.log("🔄 启动三重识别策略...");

    // 第一步：原图识别
    console.log("📸 第一步：原图识别");
    var 原图OCR结果 = paddle.ocr(屏幕图像, 配置.CPU核心数, 配置.使用快速模型);
    var 原图处理结果 = 处理OCR结果(原图OCR结果, 配置, "原图");

    console.log("📊 原图识别结果: " + 原图处理结果.符号数量 + " 个符号");

    // 输出原图识别结果
    if (原图处理结果.符号数量 > 0) {
        console.log("🔣 原图识别符号:");
        for (var i = 0; i < 原图处理结果.符号分类.符号.length; i++) {
            var 符号 = 原图处理结果.符号分类.符号[i];
            console.log("  [" + (i + 1) + "] \"" + 符号.符号 + "\" (置信度: " + 符号.置信度.toFixed(3) + ")");
        }
    } else {
        console.log("  ❌ 原图未识别到符号");
    }

    // 延时1秒
    console.log("⏰ 延时1秒后进行二值化识别...");
    sleep(1000);

    // 第二步：重新截图并进行二值化识别
    console.log("📸 第二步：重新截图进行二值化识别");
    var 新屏幕图像1 = images.captureScreen();
    if (!新屏幕图像1) {
        console.error("重新截图失败，跳过二值化识别");
        return 原图处理结果;
    }

    var 二值化图像 = 图像二值化处理(新屏幕图像1, 配置);
    var 二值化OCR结果 = paddle.ocr(二值化图像, 配置.CPU核心数, 配置.使用快速模型);
    var 二值化处理结果 = 处理OCR结果(二值化OCR结果, 配置, "二值化");

    console.log("📊 二值化识别结果: " + 二值化处理结果.符号数量 + " 个符号");

    // 输出二值化识别结果
    if (二值化处理结果.符号数量 > 0) {
        console.log("🔣 二值化识别符号:");
        for (var j = 0; j < 二值化处理结果.符号分类.符号.length; j++) {
            var 符号2 = 二值化处理结果.符号分类.符号[j];
            console.log("  [" + (j + 1) + "] \"" + 符号2.符号 + "\" (置信度: " + 符号2.置信度.toFixed(3) + ")");
        }
    } else {
        console.log("  ❌ 二值化未识别到符号");
    }

    // 延时1秒
    console.log("⏰ 延时1秒后进行反向二值化识别...");
    sleep(1000);

    // 第三步：重新截图并进行反向二值化识别
    console.log("📸 第三步：重新截图进行反向二值化识别");
    var 新屏幕图像2 = images.captureScreen();
    if (!新屏幕图像2) {
        console.error("重新截图失败，跳过反向二值化识别");
        // 合并前两步结果
        var 部分合并结果 = 合并OCR结果(原图处理结果, 二值化处理结果);

        // 释放资源
        if (二值化图像 !== 新屏幕图像1) {
            二值化图像.recycle();
        }
        新屏幕图像1.recycle();

        return 部分合并结果;
    }

    var 反向二值化图像 = 反向二值化处理(新屏幕图像2, 配置);
    var 反向二值化OCR结果 = paddle.ocr(反向二值化图像, 配置.CPU核心数, 配置.使用快速模型);
    var 反向二值化处理结果 = 处理OCR结果(反向二值化OCR结果, 配置, "反向二值化");

    console.log("📊 反向二值化识别结果: " + 反向二值化处理结果.符号数量 + " 个符号");

    // 输出反向二值化识别结果
    if (反向二值化处理结果.符号数量 > 0) {
        console.log("🔣 反向二值化识别符号:");
        for (var k = 0; k < 反向二值化处理结果.符号分类.符号.length; k++) {
            var 符号3 = 反向二值化处理结果.符号分类.符号[k];
            console.log("  [" + (k + 1) + "] \"" + 符号3.符号 + "\" (置信度: " + 符号3.置信度.toFixed(3) + ")");
        }
    } else {
        console.log("  ❌ 反向二值化未识别到符号");
    }

    // 延时1秒
    console.log("⏰ 延时1秒后进行图片模板匹配...");
    sleep(1000);

    // 第四步：重新截图并进行图片模板匹配
    console.log("📸 第四步：重新截图进行图片模板匹配");
    var 新屏幕图像3 = images.captureScreen();
    if (!新屏幕图像3) {
        console.error("重新截图失败，跳过图片模板匹配");
        // 合并前三步结果
        var 部分合并结果2 = 合并三重OCR结果(原图处理结果, 二值化处理结果, 反向二值化处理结果);

        // 释放资源
        if (二值化图像 !== 新屏幕图像1) {
            二值化图像.recycle();
        }
        if (反向二值化图像 !== 新屏幕图像2) {
            反向二值化图像.recycle();
        }
        新屏幕图像1.recycle();
        新屏幕图像2.recycle();

        return 部分合并结果2;
    }

    var 图片匹配结果 = 图片模板匹配识别(新屏幕图像3, 配置);

    console.log("📊 图片模板匹配结果: " + 图片匹配结果.符号数量 + " 个符号");

    // 输出图片模板匹配结果
    if (图片匹配结果.符号数量 > 0) {
        console.log("🔣 图片模板匹配符号:");
        for (var l = 0; l < 图片匹配结果.符号分类.符号.length; l++) {
            var 符号4 = 图片匹配结果.符号分类.符号[l];
            console.log("  [" + (l + 1) + "] \"" + 符号4.符号 + "\" (置信度: " + 符号4.置信度.toFixed(3) + ")");
        }
    } else {
        console.log("  ❌ 图片模板匹配未识别到符号");
    }

    // 第五步：四重合并结果
    console.log("🔄 合并四重识别结果...");
    var 合并结果 = 合并四重OCR结果(原图处理结果, 二值化处理结果, 反向二值化处理结果, 图片匹配结果);

    console.log("✅ 四重识别完成，最终结果: " + 合并结果.符号数量 + " 个符号");

    // 释放资源
    if (二值化图像 !== 新屏幕图像1) {
        二值化图像.recycle();
    }
    if (反向二值化图像 !== 新屏幕图像2) {
        反向二值化图像.recycle();
    }
    新屏幕图像1.recycle();
    新屏幕图像2.recycle();
    新屏幕图像3.recycle();

    return 合并结果;
}

/**
 * 图片模板匹配识别 - 遍历指定目录下的图片进行模板匹配
 */
function 图片模板匹配识别(屏幕图像, 配置) {
    try {
        if (!配置.启用图片匹配) {
            console.log("📸 跳过图片模板匹配");
            return {
                成功: true,
                符号数量: 0,
                符号列表: [],
                置信度列表: [],
                坐标列表: [],
                符号分类: { 符号: [] },
                平均置信度: 0,
                完整文本: ""
            };
        }

        console.log("📸 开始图片模板匹配，阈值: " + 配置.图片匹配阈值);

        // 直接使用相对路径（AutoX.js内置函数支持相对路径）
        var 模板目录路径 = 配置.图片模板路径;
        console.log("📂 图片模板相对路径: " + 模板目录路径);

        // 检查目录是否存在
        if (!files.exists(模板目录路径) || !files.isDir(模板目录路径)) {
            console.error("图片模板目录不存在: " + 模板目录路径);
            return {
                成功: false,
                符号数量: 0,
                符号列表: [],
                置信度列表: [],
                坐标列表: [],
                符号分类: { 符号: [] },
                平均置信度: 0,
                完整文本: "",
                错误: "模板目录不存在"
            };
        }

        // 获取目录下所有图片文件（使用相对路径）
        var 图片文件列表 = files.listDir(模板目录路径, function(文件名) {
            // 检查是否为文件（排除子目录）
            var 完整路径 = 模板目录路径 + "/" + 文件名;
            if (!files.isFile(完整路径)) {
                return false;
            }
            // 检查文件扩展名
            var 扩展名 = files.getExtension(文件名).toLowerCase();
            return 扩展名 === "png" || 扩展名 === "jpg" || 扩展名 === "jpeg";
        });

        console.log("📋 找到图片模板文件: " + 图片文件列表.length + " 个");

        // 显示找到的图片文件列表
        if (图片文件列表.length > 0) {
            console.log("📋 图片文件列表:");
            for (var fileIndex = 0; fileIndex < 图片文件列表.length; fileIndex++) {
                console.log("  [" + (fileIndex + 1) + "] " + 图片文件列表[fileIndex]);
            }
        }

        var 匹配结果列表 = [];
        var 总置信度 = 0;
        var 有效匹配数量 = 0;

        // 遍历每个图片模板进行匹配
        for (var i = 0; i < 图片文件列表.length; i++) {
            var 文件名 = 图片文件列表[i];
            var 模板路径 = 模板目录路径 + "/" + 文件名;

            console.log("🔍 匹配模板: " + 文件名);

            // 读取模板图片（使用相对路径）
            var 模板图像 = images.read(模板路径);
            if (!模板图像) {
                console.error("无法读取模板图片: " + 模板路径);
                continue;
            }

            // 进行图片匹配
            var 匹配结果 = images.findImage(屏幕图像, 模板图像, {
                threshold: 配置.图片匹配阈值
            });

            if (匹配结果) {
                // 获取符号名称（去掉扩展名）
                var 符号名称 = files.getNameWithoutExtension(文件名);
                // 使用匹配结果的相似度作为置信度，如果没有则使用阈值
                var 置信度 = 匹配结果.similarity || 配置.图片匹配阈值;

                console.log("✅ 匹配成功: \"" + 符号名称 + "\" 位置(" + 匹配结果.x + ", " + 匹配结果.y + ") 相似度:" + 置信度.toFixed(3));

                // 构建匹配结果
                var 匹配项 = {
                    符号: 符号名称,
                    置信度: 置信度,
                    坐标: {
                        left: 匹配结果.x,
                        top: 匹配结果.y,
                        right: 匹配结果.x + 模板图像.getWidth(),
                        bottom: 匹配结果.y + 模板图像.getHeight(),
                        centerX: 匹配结果.x + Math.floor(模板图像.getWidth() / 2),
                        centerY: 匹配结果.y + Math.floor(模板图像.getHeight() / 2)
                    },
                    来源: "图片匹配"
                };

                匹配结果列表.push(匹配项);
                总置信度 += 置信度;
                有效匹配数量++;
            } else {
                console.log("❌ 未匹配: " + 文件名);
            }

            // 释放模板图像资源
            模板图像.recycle();
        }

        // 构建返回结果
        var 符号列表 = [];
        var 置信度列表 = [];
        var 坐标列表 = [];

        for (var j = 0; j < 匹配结果列表.length; j++) {
            符号列表.push(匹配结果列表[j].符号);
            置信度列表.push(匹配结果列表[j].置信度);
            坐标列表.push(匹配结果列表[j].坐标);
        }

        console.log("✅ 图片模板匹配完成，匹配到 " + 有效匹配数量 + " 个符号");

        return {
            成功: true,
            符号数量: 有效匹配数量,
            符号列表: 符号列表,
            置信度列表: 置信度列表,
            坐标列表: 坐标列表,
            符号分类: { 符号: 匹配结果列表 },
            平均置信度: 有效匹配数量 > 0 ? (总置信度 / 有效匹配数量).toFixed(3) : 0,
            完整文本: 符号列表.join(" ")
        };

    } catch (e) {
        console.error("图片模板匹配失败:", e);
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: { 符号: [] },
            平均置信度: 0,
            完整文本: "",
            错误: e.toString()
        };
    }
}

/**
 * 合并四重OCR识别结果
 */
function 合并四重OCR结果(原图结果, 二值化结果, 反向二值化结果, 图片匹配结果) {
    var 合并符号列表 = [];
    var 合并置信度列表 = [];
    var 合并坐标列表 = [];
    var 合并符号分类 = { 符号: [] };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    // 用于去重的坐标容差
    var 坐标容差 = 20;

    // 先添加原图结果
    for (var i = 0; i < 原图结果.符号分类.符号.length; i++) {
        var 符号项 = 原图结果.符号分类.符号[i];
        符号项.来源 = "原图";
        合并符号分类.符号.push(符号项);
        合并符号列表.push(符号项.符号);
        合并置信度列表.push(符号项.置信度);
        合并坐标列表.push(符号项.坐标);
        总置信度 += 符号项.置信度;
        有效符号数量++;
    }

    // 添加二值化结果（去重）
    for (var j = 0; j < 二值化结果.符号分类.符号.length; j++) {
        var 二值化符号项 = 二值化结果.符号分类.符号[j];
        var 是重复1 = false;

        // 检查是否与已有结果重复
        for (var k = 0; k < 合并坐标列表.length; k++) {
            var 已有坐标 = 合并坐标列表[k];
            var 距离 = Math.sqrt(
                Math.pow(二值化符号项.坐标.centerX - 已有坐标.centerX, 2) +
                Math.pow(二值化符号项.坐标.centerY - 已有坐标.centerY, 2)
            );

            if (距离 <= 坐标容差) {
                是重复1 = true;
                if (二值化符号项.置信度 > 合并符号分类.符号[k].置信度) {
                    console.log("🔄 二值化替换: \"" + 合并符号分类.符号[k].符号 + "\" → \"" + 二值化符号项.符号 + "\"");
                    总置信度 -= 合并符号分类.符号[k].置信度;
                    总置信度 += 二值化符号项.置信度;

                    二值化符号项.来源 = "二值化(替换)";
                    合并符号分类.符号[k] = 二值化符号项;
                    合并符号列表[k] = 二值化符号项.符号;
                    合并置信度列表[k] = 二值化符号项.置信度;
                    合并坐标列表[k] = 二值化符号项.坐标;
                }
                break;
            }
        }

        if (!是重复1) {
            二值化符号项.来源 = "二值化";
            合并符号分类.符号.push(二值化符号项);
            合并符号列表.push(二值化符号项.符号);
            合并置信度列表.push(二值化符号项.置信度);
            合并坐标列表.push(二值化符号项.坐标);
            总置信度 += 二值化符号项.置信度;
            有效符号数量++;
        }
    }

    // 添加反向二值化结果（去重）
    for (var l = 0; l < 反向二值化结果.符号分类.符号.length; l++) {
        var 反向符号项 = 反向二值化结果.符号分类.符号[l];
        var 是重复2 = false;

        for (var m = 0; m < 合并坐标列表.length; m++) {
            var 已有坐标2 = 合并坐标列表[m];
            var 距离2 = Math.sqrt(
                Math.pow(反向符号项.坐标.centerX - 已有坐标2.centerX, 2) +
                Math.pow(反向符号项.坐标.centerY - 已有坐标2.centerY, 2)
            );

            if (距离2 <= 坐标容差) {
                是重复2 = true;
                if (反向符号项.置信度 > 合并符号分类.符号[m].置信度) {
                    console.log("🔄 反向二值化替换: \"" + 合并符号分类.符号[m].符号 + "\" → \"" + 反向符号项.符号 + "\"");
                    总置信度 -= 合并符号分类.符号[m].置信度;
                    总置信度 += 反向符号项.置信度;

                    反向符号项.来源 = "反向二值化(替换)";
                    合并符号分类.符号[m] = 反向符号项;
                    合并符号列表[m] = 反向符号项.符号;
                    合并置信度列表[m] = 反向符号项.置信度;
                    合并坐标列表[m] = 反向符号项.坐标;
                }
                break;
            }
        }

        if (!是重复2) {
            反向符号项.来源 = "反向二值化";
            合并符号分类.符号.push(反向符号项);
            合并符号列表.push(反向符号项.符号);
            合并置信度列表.push(反向符号项.置信度);
            合并坐标列表.push(反向符号项.坐标);
            总置信度 += 反向符号项.置信度;
            有效符号数量++;
        }
    }

    // 添加图片匹配结果（去重）
    for (var n = 0; n < 图片匹配结果.符号分类.符号.length; n++) {
        var 图片符号项 = 图片匹配结果.符号分类.符号[n];
        var 是重复3 = false;

        for (var o = 0; o < 合并坐标列表.length; o++) {
            var 已有坐标3 = 合并坐标列表[o];
            var 距离3 = Math.sqrt(
                Math.pow(图片符号项.坐标.centerX - 已有坐标3.centerX, 2) +
                Math.pow(图片符号项.坐标.centerY - 已有坐标3.centerY, 2)
            );

            if (距离3 <= 坐标容差) {
                是重复3 = true;
                if (图片符号项.置信度 > 合并符号分类.符号[o].置信度) {
                    console.log("🔄 图片匹配替换: \"" + 合并符号分类.符号[o].符号 + "\" → \"" + 图片符号项.符号 + "\"");
                    总置信度 -= 合并符号分类.符号[o].置信度;
                    总置信度 += 图片符号项.置信度;

                    图片符号项.来源 = "图片匹配(替换)";
                    合并符号分类.符号[o] = 图片符号项;
                    合并符号列表[o] = 图片符号项.符号;
                    合并置信度列表[o] = 图片符号项.置信度;
                    合并坐标列表[o] = 图片符号项.坐标;
                }
                break;
            }
        }

        if (!是重复3) {
            图片符号项.来源 = "图片匹配";
            合并符号分类.符号.push(图片符号项);
            合并符号列表.push(图片符号项.符号);
            合并置信度列表.push(图片符号项.置信度);
            合并坐标列表.push(图片符号项.坐标);
            总置信度 += 图片符号项.置信度;
            有效符号数量++;
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 合并符号列表,
        置信度列表: 合并置信度列表,
        坐标列表: 合并坐标列表,
        符号分类: 合并符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 合并符号列表.join(" "),
        原图结果数量: 原图结果.符号数量,
        二值化结果数量: 二值化结果.符号数量,
        反向二值化结果数量: 反向二值化结果.符号数量,
        图片匹配结果数量: 图片匹配结果.符号数量
    };
}

/**
 * 合并三重OCR识别结果
 */
function 合并三重OCR结果(原图结果, 二值化结果, 反向二值化结果) {
    var 合并符号列表 = [];
    var 合并置信度列表 = [];
    var 合并坐标列表 = [];
    var 合并符号分类 = { 符号: [] };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    // 用于去重的坐标容差
    var 坐标容差 = 20;

    // 先添加原图结果
    for (var i = 0; i < 原图结果.符号分类.符号.length; i++) {
        var 符号项 = 原图结果.符号分类.符号[i];
        符号项.来源 = "原图";
        合并符号分类.符号.push(符号项);
        合并符号列表.push(符号项.符号);
        合并置信度列表.push(符号项.置信度);
        合并坐标列表.push(符号项.坐标);
        总置信度 += 符号项.置信度;
        有效符号数量++;
    }

    // 添加二值化结果（去重）
    for (var j = 0; j < 二值化结果.符号分类.符号.length; j++) {
        var 二值化符号项 = 二值化结果.符号分类.符号[j];
        var 是重复1 = false;

        // 检查是否与已有结果重复
        for (var k = 0; k < 合并坐标列表.length; k++) {
            var 已有坐标 = 合并坐标列表[k];
            var 距离 = Math.sqrt(
                Math.pow(二值化符号项.坐标.centerX - 已有坐标.centerX, 2) +
                Math.pow(二值化符号项.坐标.centerY - 已有坐标.centerY, 2)
            );

            if (距离 <= 坐标容差) {
                是重复1 = true;
                // 如果二值化置信度更高，替换现有结果
                if (二值化符号项.置信度 > 合并符号分类.符号[k].置信度) {
                    console.log("🔄 二值化替换: \"" + 合并符号分类.符号[k].符号 + "\" → \"" + 二值化符号项.符号 + "\"");
                    总置信度 -= 合并符号分类.符号[k].置信度;
                    总置信度 += 二值化符号项.置信度;

                    二值化符号项.来源 = "二值化(替换)";
                    合并符号分类.符号[k] = 二值化符号项;
                    合并符号列表[k] = 二值化符号项.符号;
                    合并置信度列表[k] = 二值化符号项.置信度;
                    合并坐标列表[k] = 二值化符号项.坐标;
                }
                break;
            }
        }

        // 如果不重复，添加到结果中
        if (!是重复1) {
            二值化符号项.来源 = "二值化";
            合并符号分类.符号.push(二值化符号项);
            合并符号列表.push(二值化符号项.符号);
            合并置信度列表.push(二值化符号项.置信度);
            合并坐标列表.push(二值化符号项.坐标);
            总置信度 += 二值化符号项.置信度;
            有效符号数量++;
        }
    }

    // 添加反向二值化结果（去重）
    for (var l = 0; l < 反向二值化结果.符号分类.符号.length; l++) {
        var 反向符号项 = 反向二值化结果.符号分类.符号[l];
        var 是重复2 = false;

        // 检查是否与已有结果重复
        for (var m = 0; m < 合并坐标列表.length; m++) {
            var 已有坐标2 = 合并坐标列表[m];
            var 距离2 = Math.sqrt(
                Math.pow(反向符号项.坐标.centerX - 已有坐标2.centerX, 2) +
                Math.pow(反向符号项.坐标.centerY - 已有坐标2.centerY, 2)
            );

            if (距离2 <= 坐标容差) {
                是重复2 = true;
                // 如果反向二值化置信度更高，替换现有结果
                if (反向符号项.置信度 > 合并符号分类.符号[m].置信度) {
                    console.log("🔄 反向二值化替换: \"" + 合并符号分类.符号[m].符号 + "\" → \"" + 反向符号项.符号 + "\"");
                    总置信度 -= 合并符号分类.符号[m].置信度;
                    总置信度 += 反向符号项.置信度;

                    反向符号项.来源 = "反向二值化(替换)";
                    合并符号分类.符号[m] = 反向符号项;
                    合并符号列表[m] = 反向符号项.符号;
                    合并置信度列表[m] = 反向符号项.置信度;
                    合并坐标列表[m] = 反向符号项.坐标;
                }
                break;
            }
        }

        // 如果不重复，添加到结果中
        if (!是重复2) {
            反向符号项.来源 = "反向二值化";
            合并符号分类.符号.push(反向符号项);
            合并符号列表.push(反向符号项.符号);
            合并置信度列表.push(反向符号项.置信度);
            合并坐标列表.push(反向符号项.坐标);
            总置信度 += 反向符号项.置信度;
            有效符号数量++;
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 合并符号列表,
        置信度列表: 合并置信度列表,
        坐标列表: 合并坐标列表,
        符号分类: 合并符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 合并符号列表.join(" "),
        原图结果数量: 原图结果.符号数量,
        二值化结果数量: 二值化结果.符号数量,
        反向二值化结果数量: 反向二值化结果.符号数量
    };
}

/**
 * 合并两次OCR识别结果
 */
function 合并OCR结果(原图结果, 二值化结果) {
    var 合并符号列表 = [];
    var 合并置信度列表 = [];
    var 合并坐标列表 = [];
    var 合并符号分类 = { 符号: [] };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    // 用于去重的坐标容差
    var 坐标容差 = 20;

    // 先添加原图结果
    for (var i = 0; i < 原图结果.符号分类.符号.length; i++) {
        var 符号项 = 原图结果.符号分类.符号[i];
        符号项.来源 = "原图";
        合并符号分类.符号.push(符号项);
        合并符号列表.push(符号项.符号);
        合并置信度列表.push(符号项.置信度);
        合并坐标列表.push(符号项.坐标);
        总置信度 += 符号项.置信度;
        有效符号数量++;
    }

    // 添加二值化结果（去重）
    for (var j = 0; j < 二值化结果.符号分类.符号.length; j++) {
        var 二值化符号项 = 二值化结果.符号分类.符号[j];
        var 是重复 = false;

        // 检查是否与原图结果重复（基于坐标距离）
        for (var k = 0; k < 合并坐标列表.length; k++) {
            var 已有坐标 = 合并坐标列表[k];
            var 距离 = Math.sqrt(
                Math.pow(二值化符号项.坐标.centerX - 已有坐标.centerX, 2) +
                Math.pow(二值化符号项.坐标.centerY - 已有坐标.centerY, 2)
            );

            if (距离 <= 坐标容差) {
                是重复 = true;
                // 如果二值化置信度更高，替换现有结果
                if (二值化符号项.置信度 > 合并符号分类.符号[k].置信度) {
                    console.log("🔄 替换低置信度结果: \"" + 合并符号分类.符号[k].符号 + "\" → \"" + 二值化符号项.符号 + "\"");
                    总置信度 -= 合并符号分类.符号[k].置信度;
                    总置信度 += 二值化符号项.置信度;

                    二值化符号项.来源 = "二值化(替换)";
                    合并符号分类.符号[k] = 二值化符号项;
                    合并符号列表[k] = 二值化符号项.符号;
                    合并置信度列表[k] = 二值化符号项.置信度;
                    合并坐标列表[k] = 二值化符号项.坐标;
                }
                break;
            }
        }

        // 如果不重复，添加到结果中
        if (!是重复) {
            二值化符号项.来源 = "二值化";
            合并符号分类.符号.push(二值化符号项);
            合并符号列表.push(二值化符号项.符号);
            合并置信度列表.push(二值化符号项.置信度);
            合并坐标列表.push(二值化符号项.坐标);
            总置信度 += 二值化符号项.置信度;
            有效符号数量++;
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 合并符号列表,
        置信度列表: 合并置信度列表,
        坐标列表: 合并坐标列表,
        符号分类: 合并符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 合并符号列表.join(" "),
        原图结果数量: 原图结果.符号数量,
        二值化结果数量: 二值化结果.符号数量
    };
}

/**
 * 处理OCR识别结果 - 专门用于符号识别
 */
function 处理OCR结果(OCR结果, 配置, 来源标识) {
    var 来源 = 来源标识 || "未知";
    console.log("🔍 " + 来源 + "OCR识别结果数量: " + (OCR结果 ? OCR结果.length : 0));

    if (!OCR结果 || OCR结果.length === 0) {
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: { 符号: [] },
            平均置信度: 0,
            完整文本: "",
            消息: "未识别到符号"
        };
    }

    // 调试：显示所有OCR识别结果
    console.log("📋 所有OCR识别内容:");
    for (var j = 0; j < OCR结果.length; j++) {
        var 调试项 = OCR结果[j];
        console.log("  [" + (j + 1) + "] \"" + 调试项.text + "\" (置信度: " + 调试项.confidence.toFixed(3) + ")");
    }

    var 符号列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 符号分类 = { 符号: [] };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];
        var 识别文字 = 项目.text;

        // 符号过滤和分类
        var 符号信息 = 分析符号类型(识别文字);

        console.log("🔍 分析 \"" + 识别文字 + "\": " + (符号信息.是符号 ? "✅符号" : "❌" + 符号信息.类型) + " (置信度: " + 项目.confidence.toFixed(3) + ")");

        if (符号信息.是符号) {
            符号列表.push(识别文字);
            置信度列表.push(项目.confidence);

            // 计算中心坐标
            var 中心坐标 = {
                left: 项目.bounds.left,
                top: 项目.bounds.top,
                right: 项目.bounds.right,
                bottom: 项目.bounds.bottom,
                centerX: Math.round((项目.bounds.left + 项目.bounds.right) / 2),
                centerY: Math.round((项目.bounds.top + 项目.bounds.bottom) / 2),
                width: 项目.bounds.right - 项目.bounds.left,
                height: 项目.bounds.bottom - 项目.bounds.top
            };
            坐标列表.push(中心坐标);

            // 符号分类
            var 符号项 = {
                符号: 识别文字,
                类型: 符号信息.类型,
                描述: 符号信息.描述,
                置信度: 项目.confidence,
                坐标: 中心坐标
            };

            // 只保存符号
            符号分类.符号.push(符号项);

            总置信度 += 项目.confidence;
            有效符号数量++;
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 符号列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        符号分类: 符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 符号列表.join(" "),
        原始OCR结果: OCR结果
    };
}

/**
 * 分析符号类型 - 放宽符号识别条件
 */
function 分析符号类型(文字) {
    // 完全禁止识别中文字符

    // 排除任何包含中文字符的内容（优先级最高）
    if (/[\u4e00-\u9fa5]/.test(文字)) {
        return {
            是符号: false,
            类型: "中文",
            描述: "包含中文字符"
        };
    }

    // 检查是否为常见的按钮文字（优先识别为符号）
    var 按钮文字 = ["close", "X"];
    if (按钮文字.indexOf(文字.toLowerCase()) !== -1) {
        return {
            是符号: true,
            类型: "符号",
            描述: "按钮文字符号"
        };
    }

    // 排除包含空格的英文句子
    if (/\s/.test(文字) && /[a-zA-Z]/.test(文字)) {
        return {
            是符号: false,
            类型: "英文句子",
            描述: "包含空格的英文句子"
        };
    }

    // 排除长英文单词（长度>6的纯英文）
    if (/^[a-zA-Z]{7,}$/.test(文字)) {
        return {
            是符号: false,
            类型: "长英文",
            描述: "长英文单词"
        };
    }

    // 排除包含多个英文单词的内容（包含×连接符的长英文）
    if (文字.length > 10 && /[a-zA-Z].*×.*[a-zA-Z]/.test(文字)) {
        return {
            是符号: false,
            类型: "英文句子",
            描述: "包含连接符的英文句子"
        };
    }

    // 识别短内容（长度<=3且不包含中文）
    if (文字.length <= 3) {
        return {
            是符号: true,
            类型: "符号",
            描述: "短符号"
        };
    }

    // 识别单个字符（不包含中文）
    if (文字.length === 1) {
        return {
            是符号: true,
            类型: "符号",
            描述: "单个符号"
        };
    }

    // 包含数字的内容（不包含中文）
    if (/\d/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含数字的符号"
        };
    }

    // 包含特殊字符的内容（不包含中文）
    if (/[^a-zA-Z0-9\s]/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含特殊字符"
        };
    }

    // 其他视为文字
    return {
        是符号: false,
        类型: "文字",
        描述: "普通文字"
    };
}

// 测试代码 - 直接运行
console.log("🎯 开始测试Paddle OCR精准符号识别功能...");

// 精准符号识别测试
console.log("\n=== 精准符号识别测试 ===");
var 结果 = 看广告();

if (结果.成功) {
    console.log("✅ 符号识别成功！");
    console.log("📊 识别到 " + 结果.识别结果.符号数量 + " 个符号");
    console.log("📝 完整符号: " + 结果.识别结果.完整文本);
    console.log("🎯 平均置信度: " + 结果.识别结果.平均置信度);

    // 输出符号识别结果
    var 分类 = 结果.识别结果.符号分类;
    console.log("\n📋 符号识别结果:");
    console.log("  🔣 识别到符号: " + 分类.符号.length + " 个");

    // 显示四重识别统计
    if (结果.识别结果.图片匹配结果数量 !== undefined) {
        console.log("  📊 原图识别: " + 结果.识别结果.原图结果数量 + " 个");
        console.log("  📊 二值化识别: " + 结果.识别结果.二值化结果数量 + " 个");
        console.log("  📊 反向二值化识别: " + 结果.识别结果.反向二值化结果数量 + " 个");
        console.log("  📊 图片模板匹配: " + 结果.识别结果.图片匹配结果数量 + " 个");
        console.log("  🔄 四重合并后总数: " + 分类.符号.length + " 个");
    } else if (结果.识别结果.反向二值化结果数量 !== undefined) {
        console.log("  📊 原图识别: " + 结果.识别结果.原图结果数量 + " 个");
        console.log("  📊 二值化识别: " + 结果.识别结果.二值化结果数量 + " 个");
        console.log("  📊 反向二值化识别: " + 结果.识别结果.反向二值化结果数量 + " 个");
        console.log("  🔄 三重合并后总数: " + 分类.符号.length + " 个");
    } else if (结果.识别结果.二值化结果数量 !== undefined) {
        console.log("  📊 原图识别: " + 结果.识别结果.原图结果数量 + " 个");
        console.log("  � 二值化识别: " + 结果.识别结果.二值化结果数量 + " 个");
        console.log("  �🔄 三重合并后总数: " + 分类.符号.length + " 个");
    }

    // 显示所有识别到的符号
    if (分类.符号.length > 0) {
        console.log("\n🔣 符号详情:");
        for (var i = 0; i < 分类.符号.length; i++) {
            var 符号 = 分类.符号[i];
            console.log("  [" + (i + 1) + "] 符号: \"" + 符号.符号 + "\"");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("      区域: (" + 符号.坐标.left + ", " + 符号.坐标.top + ", " + 符号.坐标.right + ", " + 符号.坐标.bottom + ")");
            if (符号.来源) {
                console.log("      来源: " + 符号.来源);
            }
            console.log("");
        }
    } else {
        console.log("  ❌ 未识别到任何符号");
    }

} else {
    console.log("❌ 符号识别失败: " + 结果.错误);
}

console.log("📊 Paddle OCR精准符号识别功能测试完成");