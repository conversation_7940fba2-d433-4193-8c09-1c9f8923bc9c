/**
 * Paddle OCR 看广告识别模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：智能识别广告区域文字，支持多区域检测和点击推荐
 * 作者：Magic项目组
 * 版本：v1.0
 */

/**
 * 看广告 - 使用Paddle OCR识别广告区域文字
 * @param {string|Array|Object} 区域或选项 - 区域名称、坐标数组或完整选项对象
 * @param {Object} 高级选项 - 高级配置选项（可选）
 * @returns {Object} 识别结果和推荐操作
 *
 * 调用方式：
 * 1. 看广告("右广告")                    - 识别指定区域
 * 2. 看广告([425, 2, 110, 176])         - 识别指定坐标区域
 * 3. 看广告(null)                       - 识别所有预设区域
 * 4. 看广告({区域: "左广告", 置信度: 0.8}) - 完整选项配置
 */
function 看广告(区域或选项, 高级选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(区域或选项, 高级选项);

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        traceLog("开始OCR看广告识别，配置: " + JSON.stringify(配置));

        var 识别结果;

        if (配置.识别模式 === "单区域") {
            识别结果 = 识别单个区域(屏幕图像, 配置);
        } else {
            识别结果 = 识别多个区域(屏幕图像, 配置);
        }

        // 释放资源
        屏幕图像.recycle();

        return 识别结果;

    } catch (e) {
        console.error("看广告OCR识别失败:", e);
        traceLog("OCR识别错误详情: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "OCR识别失败"
        };
    }
}

/**
 * 解析函数参数，统一处理不同的调用方式
 */
function 解析参数(区域或选项, 高级选项) {
    var 默认配置 = {
        CPU核心数: 4,
        使用快速模型: true,
        置信度阈值: 0.7,
        识别模式: "单区域",
        保存调试图片: false,
        点击延迟: 500,
        关键词过滤: ["播放", ">>", "▶", "观看", "看广告", "跳过", "×", "X", "关闭"]
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理第一个参数
    if (区域或选项 === null || 区域或选项 === undefined) {
        // 识别所有区域
        配置.识别模式 = "多区域";
        配置.目标区域 = 获取所有预设区域();
    } else if (typeof 区域或选项 === "string") {
        // 区域名称
        配置.识别模式 = "单区域";
        配置.目标区域 = 获取区域坐标(区域或选项);
        配置.区域名称 = 区域或选项;
    } else if (Array.isArray(区域或选项) && 区域或选项.length === 4) {
        // 坐标数组
        配置.识别模式 = "单区域";
        配置.目标区域 = 区域或选项;
        配置.区域名称 = "自定义区域";
    } else if (typeof 区域或选项 === "object") {
        // 完整选项对象
        Object.assign(配置, 区域或选项);

        if (配置.区域) {
            if (typeof 配置.区域 === "string") {
                配置.目标区域 = 获取区域坐标(配置.区域);
                配置.区域名称 = 配置.区域;
                配置.识别模式 = "单区域";
            } else if (Array.isArray(配置.区域)) {
                配置.目标区域 = 配置.区域;
                配置.区域名称 = "自定义区域";
                配置.识别模式 = "单区域";
            }
        } else {
            配置.识别模式 = "多区域";
            配置.目标区域 = 获取所有预设区域();
        }
    }

    // 处理高级选项
    if (高级选项 && typeof 高级选项 === "object") {
        Object.assign(配置, 高级选项);
    }

    return 配置;
}

/**
 * 获取预设区域坐标
 */
function 获取区域坐标(区域名称) {
    var 区域映射 = {
        "右广告": [425, 2, 110, 176],
        "左广告": [5, 2, 110, 176],
        "close区域": [480, 50, 60, 60],
        "中央广告": [200, 300, 140, 200],
        "底部广告": [100, 800, 340, 100],
        "顶部广告": [100, 50, 340, 100]
    };

    if (区域映射[区域名称]) {
        return 区域映射[区域名称];
    } else {
        throw new Error("未知的区域名称: " + 区域名称);
    }
}

/**
 * 获取所有预设区域
 */
function 获取所有预设区域() {
    return [
        {名称: "右广告", 坐标: [425, 2, 110, 176]},
        {名称: "左广告", 坐标: [5, 2, 110, 176]},
        {名称: "close区域", 坐标: [480, 50, 60, 60]}
    ];
}

/**
 * 识别单个区域
 */
function 识别单个区域(屏幕图像, 配置) {
    var 区域坐标 = 配置.目标区域;
    var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);

    try {
        // 使用Paddle OCR识别
        var OCR结果 = paddle.ocr(区域图像, 配置.CPU核心数, 配置.使用快速模型);

        // 处理识别结果
        var 处理结果 = 处理OCR结果(OCR结果, 配置, 区域坐标);

        // 保存调试图片
        if (配置.保存调试图片) {
            保存调试图片(区域图像, 配置.区域名称 + "_ocr_debug");
        }

        return {
            成功: true,
            区域名称: 配置.区域名称,
            区域坐标: 区域坐标,
            识别结果: 处理结果,
            推荐操作: 生成推荐操作(处理结果, 区域坐标, 配置),
            消息: "单区域OCR识别完成"
        };

    } finally {
        区域图像.recycle();
    }
}

/**
 * 识别多个区域
 */
function 识别多个区域(屏幕图像, 配置) {
    var 所有结果 = [];
    var 最佳推荐 = null;
    var 最高优先级 = -1;

    for (var i = 0; i < 配置.目标区域.length; i++) {
        var 区域信息 = 配置.目标区域[i];
        var 区域坐标 = 区域信息.坐标;
        var 区域名称 = 区域信息.名称;

        try {
            var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);

            // OCR识别
            var OCR结果 = paddle.ocr(区域图像, 配置.CPU核心数, 配置.使用快速模型);
            var 处理结果 = 处理OCR结果(OCR结果, 配置, 区域坐标);

            var 区域结果 = {
                区域名称: 区域名称,
                区域坐标: 区域坐标,
                识别结果: 处理结果,
                推荐操作: 生成推荐操作(处理结果, 区域坐标, 配置)
            };

            所有结果.push(区域结果);

            // 评估优先级
            var 优先级 = 评估区域优先级(区域结果, 配置);
            if (优先级 > 最高优先级) {
                最高优先级 = 优先级;
                最佳推荐 = 区域结果.推荐操作;
            }

            // 保存调试图片
            if (配置.保存调试图片) {
                保存调试图片(区域图像, 区域名称 + "_ocr_debug");
            }

            区域图像.recycle();

        } catch (e) {
            console.error("区域识别失败:", 区域名称, e);
            所有结果.push({
                区域名称: 区域名称,
                区域坐标: 区域坐标,
                识别结果: {成功: false, 错误: e.toString()},
                推荐操作: null
            });
        }
    }

    return {
        成功: true,
        识别模式: "多区域",
        区域数量: 所有结果.length,
        所有结果: 所有结果,
        最佳推荐: 最佳推荐,
        消息: "多区域OCR识别完成"
    };
}

/**
 * 处理OCR识别结果
 */
function 处理OCR结果(OCR结果, 配置, 区域坐标) {
    if (!OCR结果 || OCR结果.length === 0) {
        return {
            成功: false,
            文字数量: 0,
            文字列表: [],
            置信度列表: [],
            坐标列表: [],
            关键词匹配: [],
            平均置信度: 0,
            完整文本: "",
            消息: "未识别到文字"
        };
    }

    var 文字列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 关键词匹配 = [];
    var 总置信度 = 0;

    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];

        // 置信度过滤
        if (项目.confidence >= 配置.置信度阈值) {
            文字列表.push(项目.text);
            置信度列表.push(项目.confidence);

            // 转换为全屏坐标
            var 全屏坐标 = 转换为全屏坐标(项目.bounds, 区域坐标);
            坐标列表.push(全屏坐标);

            总置信度 += 项目.confidence;

            // 检查关键词匹配
            var 匹配关键词 = 检查关键词匹配(项目.text, 配置.关键词过滤);
            if (匹配关键词.length > 0) {
                关键词匹配.push({
                    文字: 项目.text,
                    关键词: 匹配关键词,
                    置信度: 项目.confidence,
                    坐标: 全屏坐标
                });
            }
        }
    }

    return {
        成功: true,
        文字数量: 文字列表.length,
        文字列表: 文字列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        关键词匹配: 关键词匹配,
        平均置信度: 文字列表.length > 0 ? (总置信度 / 文字列表.length).toFixed(3) : 0,
        完整文本: 文字列表.join(" "),
        原始OCR结果: OCR结果
    };
}

/**
 * 转换区域坐标为全屏坐标
 */
function 转换为全屏坐标(区域内坐标, 区域偏移) {
    return {
        left: 区域内坐标.left + 区域偏移[0],
        top: 区域内坐标.top + 区域偏移[1],
        right: 区域内坐标.right + 区域偏移[0],
        bottom: 区域内坐标.bottom + 区域偏移[1],
        centerX: Math.round((区域内坐标.left + 区域内坐标.right) / 2) + 区域偏移[0],
        centerY: Math.round((区域内坐标.top + 区域内坐标.bottom) / 2) + 区域偏移[1]
    };
}

/**
 * 检查关键词匹配
 */
function 检查关键词匹配(文字, 关键词列表) {
    var 匹配结果 = [];

    for (var i = 0; i < 关键词列表.length; i++) {
        var 关键词 = 关键词列表[i];
        if (文字.indexOf(关键词) !== -1) {
            匹配结果.push(关键词);
        }
    }

    return 匹配结果;
}

/**
 * 生成推荐操作
 */
function 生成推荐操作(识别结果, 区域坐标, 配置) {
    if (!识别结果.成功 || 识别结果.关键词匹配.length === 0) {
        return null;
    }

    // 选择最佳匹配项（置信度最高的关键词匹配）
    var 最佳匹配 = null;
    var 最高置信度 = 0;

    for (var i = 0; i < 识别结果.关键词匹配.length; i++) {
        var 匹配项 = 识别结果.关键词匹配[i];
        if (匹配项.置信度 > 最高置信度) {
            最高置信度 = 匹配项.置信度;
            最佳匹配 = 匹配项;
        }
    }

    if (最佳匹配) {
        return {
            动作: "点击",
            坐标: {
                x: 最佳匹配.坐标.centerX,
                y: 最佳匹配.坐标.centerY
            },
            文字: 最佳匹配.文字,
            关键词: 最佳匹配.关键词,
            置信度: 最佳匹配.置信度,
            延迟: 配置.点击延迟,
            执行函数: function() {
                click(最佳匹配.坐标.centerX, 最佳匹配.坐标.centerY);
                sleep(配置.点击延迟);
                return true;
            }
        };
    }

    return null;
}

/**
 * 评估区域优先级
 */
function 评估区域优先级(区域结果, 配置) {
    if (!区域结果.推荐操作) {
        return 0;
    }

    var 基础分数 = 区域结果.推荐操作.置信度 * 100;

    // 区域权重
    var 区域权重 = {
        "右广告": 1.2,
        "左广告": 1.1,
        "close区域": 1.5,
        "中央广告": 1.3
    };

    var 权重 = 区域权重[区域结果.区域名称] || 1.0;

    // 关键词权重
    var 关键词权重 = {
        "播放": 1.5,
        ">>": 1.4,
        "▶": 1.4,
        "观看": 1.3,
        "×": 1.6,
        "X": 1.6,
        "关闭": 1.6
    };

    var 最高关键词权重 = 1.0;
    for (var i = 0; i < 区域结果.推荐操作.关键词.length; i++) {
        var 关键词 = 区域结果.推荐操作.关键词[i];
        var 当前权重 = 关键词权重[关键词] || 1.0;
        if (当前权重 > 最高关键词权重) {
            最高关键词权重 = 当前权重;
        }
    }

    return 基础分数 * 权重 * 最高关键词权重;
}

/**
 * 保存调试图片
 */
function 保存调试图片(图像, 文件名) {
    try {
        var 保存路径 = "/storage/emulated/0/Pictures/" + 文件名 + "_" + Date.now() + ".png";
        images.save(图像, 保存路径);
        traceLog("调试图片已保存: " + 保存路径);
    } catch (e) {
        console.error("保存调试图片失败:", e);
    }
}

// 导出模块
module.exports = {
    看广告: 看广告
};

// 测试代码
if (require.main === module) {
    console.log("🎯 开始测试Paddle OCR看广告功能...");

    // 测试1：识别右广告区域
    console.log("\n=== 测试1：识别右广告区域 ===");
    var 结果1 = 看广告("右广告", {置信度阈值: 0.6, 保存调试图片: true});

    if (结果1.成功) {
        console.log("✅ 右广告识别成功！");
        console.log("📊 识别到 " + 结果1.识别结果.文字数量 + " 个文字");
        console.log("📝 完整文本: " + 结果1.识别结果.完整文本);
        console.log("🎯 平均置信度: " + 结果1.识别结果.平均置信度);

        if (结果1.推荐操作) {
            console.log("🖱️ 推荐点击: (" + 结果1.推荐操作.坐标.x + ", " + 结果1.推荐操作.坐标.y + ")");
            console.log("🔑 匹配关键词: " + 结果1.推荐操作.关键词.join(", "));
        }
    } else {
        console.log("❌ 右广告识别失败: " + 结果1.错误);
    }

    // 测试2：识别所有区域
    console.log("\n=== 测试2：识别所有预设区域 ===");
    var 结果2 = 看广告(null, {置信度阈值: 0.5});

    if (结果2.成功) {
        console.log("✅ 多区域识别成功！");
        console.log("📊 检测了 " + 结果2.区域数量 + " 个区域");

        for (var i = 0; i < 结果2.所有结果.length; i++) {
            var 区域结果 = 结果2.所有结果[i];
            console.log("  🎯 " + 区域结果.区域名称 + ": " +
                       (区域结果.识别结果.成功 ?
                        "发现 " + 区域结果.识别结果.文字数量 + " 个文字" :
                        "识别失败"));
        }

        if (结果2.最佳推荐) {
            console.log("🏆 最佳推荐操作: 点击 (" + 结果2.最佳推荐.坐标.x + ", " + 结果2.最佳推荐.坐标.y + ")");
            console.log("📝 推荐理由: " + 结果2.最佳推荐.文字 + " (关键词: " + 结果2.最佳推荐.关键词.join(", ") + ")");
        }
    } else {
        console.log("❌ 多区域识别失败: " + 结果2.错误);
    }

    // 测试3：自定义区域识别
    console.log("\n=== 测试3：自定义区域识别 ===");
    var 结果3 = 看广告([200, 300, 140, 200], {
        置信度阈值: 0.4,
        关键词过滤: ["播放", "开始", "继续", "确定"]
    });

    if (结果3.成功) {
        console.log("✅ 自定义区域识别成功！");
        console.log("📝 识别文本: " + 结果3.识别结果.完整文本);
    } else {
        console.log("❌ 自定义区域识别失败: " + 结果3.错误);
    }

    // 测试4：完整选项配置
    console.log("\n=== 测试4：完整选项配置 ===");
    var 结果4 = 看广告({
        区域: "close区域",
        CPU核心数: 6,
        使用快速模型: false,
        置信度阈值: 0.8,
        关键词过滤: ["×", "X", "关闭", "跳过"],
        保存调试图片: true,
        点击延迟: 300
    });

    if (结果4.成功) {
        console.log("✅ 完整配置识别成功！");
        console.log("📊 关键词匹配数量: " + 结果4.识别结果.关键词匹配.length);
    } else {
        console.log("❌ 完整配置识别失败: " + 结果4.错误);
    }

    console.log("📊 Paddle OCR看广告功能测试完成");
}