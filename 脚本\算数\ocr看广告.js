/**
 * Paddle OCR 看广告识别模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：全屏OCR文字识别，输出文字信息、坐标和置信度
 * 作者：Magic项目组
 * 版本：v2.0
 */

/**
 * 看广告 - 使用Paddle OCR进行全屏文字识别
 * @param {Object} 选项 - 识别选项（可选）
 * @returns {Object} 识别结果包含文字信息、坐标和置信度
 *
 * 调用方式：
 * 1. 看广告()                           - 使用默认配置全屏识别
 * 2. 看广告({置信度阈值: 0.8})           - 自定义置信度阈值
 * 3. 看广告({使用快速模型: false})       - 使用精准模型
 */
function 看广告(选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(选项);

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        traceLog("开始全屏OCR识别，配置: " + JSON.stringify(配置));

        // 使用Paddle OCR进行全屏识别
        var OCR结果 = paddle.ocr(屏幕图像, 配置.CPU核心数, 配置.使用快速模型);

        // 处理识别结果
        var 处理结果 = 处理OCR结果(OCR结果, 配置);

        // 释放资源
        屏幕图像.recycle();

        return {
            成功: true,
            识别结果: 处理结果,
            消息: "全屏OCR识别完成"
        };

    } catch (e) {
        console.error("看广告OCR识别失败:", e);
        traceLog("OCR识别错误详情: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "OCR识别失败"
        };
    }
}

/**
 * 解析函数参数
 */
function 解析参数(选项) {
    var 默认配置 = {
        CPU核心数: 2,                    // 根据要求改为2
        使用快速模型: true,              // true=快速模型, false=精准模型
        置信度阈值: 0.7                 // 文字识别置信度阈值
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理选项参数
    if (选项 && typeof 选项 === "object") {
        Object.assign(配置, 选项);
    }

    return 配置;
}

/**
 * 处理OCR识别结果
 */
function 处理OCR结果(OCR结果, 配置) {
    if (!OCR结果 || OCR结果.length === 0) {
        return {
            成功: false,
            文字数量: 0,
            文字列表: [],
            置信度列表: [],
            坐标列表: [],
            平均置信度: 0,
            完整文本: "",
            消息: "未识别到文字"
        };
    }

    var 文字列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 总置信度 = 0;
    var 有效文字数量 = 0;

    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];

        // 置信度过滤
        if (项目.confidence >= 配置.置信度阈值) {
            文字列表.push(项目.text);
            置信度列表.push(项目.confidence);

            // 计算中心坐标
            var 中心坐标 = {
                left: 项目.bounds.left,
                top: 项目.bounds.top,
                right: 项目.bounds.right,
                bottom: 项目.bounds.bottom,
                centerX: Math.round((项目.bounds.left + 项目.bounds.right) / 2),
                centerY: Math.round((项目.bounds.top + 项目.bounds.bottom) / 2),
                width: 项目.bounds.right - 项目.bounds.left,
                height: 项目.bounds.bottom - 项目.bounds.top
            };
            坐标列表.push(中心坐标);

            总置信度 += 项目.confidence;
            有效文字数量++;
        }
    }

    return {
        成功: true,
        文字数量: 有效文字数量,
        文字列表: 文字列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        平均置信度: 有效文字数量 > 0 ? (总置信度 / 有效文字数量).toFixed(3) : 0,
        完整文本: 文字列表.join(" "),
        原始OCR结果: OCR结果
    };
}



// // 导出模块
// module.exports = {
//     看广告: 看广告
// };

// 测试代码 - 直接运行
console.log("🎯 开始测试Paddle OCR快速识别功能...");

// 快速识别测试
console.log("\n=== 快速识别测试 ===");
var 结果 = 看广告();

if (结果.成功) {
    console.log("✅ 快速识别成功！");
    console.log("📊 识别到 " + 结果.识别结果.文字数量 + " 个文字");
    console.log("📝 完整文本: " + 结果.识别结果.完整文本);
    console.log("🎯 平均置信度: " + 结果.识别结果.平均置信度);

    // 输出前5个识别结果的详细信息
    var 显示数量 = Math.min(5, 结果.识别结果.文字数量);
    console.log("📋 详细识别结果（前" + 显示数量 + "个）:");

    for (var i = 0; i < 显示数量; i++) {
        var 文字 = 结果.识别结果.文字列表[i];
        var 置信度 = 结果.识别结果.置信度列表[i];
        var 坐标 = 结果.识别结果.坐标列表[i];

        console.log("  [" + (i + 1) + "] 文字: \"" + 文字 + "\"");
        console.log("      置信度: " + 置信度.toFixed(3));
        console.log("      中心坐标: (" + 坐标.centerX + ", " + 坐标.centerY + ")");
        console.log("      区域: (" + 坐标.left + ", " + 坐标.top + ", " + 坐标.right + ", " + 坐标.bottom + ")");
        console.log("      尺寸: " + 坐标.width + "x" + 坐标.height);
        console.log("");
    }
} else {
    console.log("❌ 快速识别失败: " + 结果.错误);
}

console.log("📊 Paddle OCR快速识别功能测试完成");