/**
 * AutoX.js 内置OCR文字识别模块
 * 适用于雷电模拟器 540x960 DPI 240
 * 基于AutoXjs ozobiozobi v6.5.8.17
 */

// OCR配置
var OCR_CONFIG = {
    引擎类型: "paddle",           // paddle, gmlkit, tesseract
    CPU核心数: 4,                // Paddle OCR使用的CPU核心数
    使用快速模型: true,          // true=快速模型, false=精准模型
    语言: "zh",                  // Google ML Kit语言设置
    置信度阈值: 0.7             // 文字识别置信度阈值
};

/**
 * 截图并识别文字
 * @param {Object} 选项 - 识别选项
 * @param {Array} 选项.识别区域 - [x, y, width, height] 截图区域
 * @param {string} 选项.引擎 - OCR引擎类型
 * @param {number} 选项.置信度阈值 - 置信度过滤阈值
 * @returns {Object} 识别结果
 */
function 截图识别文字(选项) {
    选项 = 选项 || {};
    
    try {
        // 1. 截图
        traceLog("开始截图识别文字...");
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }
        
        // 2. 处理识别区域
        var 目标图像;
        if (选项.识别区域 && 选项.识别区域.length === 4) {
            var 区域 = 选项.识别区域;
            目标图像 = images.clip(屏幕图像, 区域[0], 区域[1], 区域[2], 区域[3]);
            traceLog("截取区域: " + JSON.stringify(区域));
        } else {
            目标图像 = 屏幕图像;
            traceLog("使用全屏截图");
        }
        
        // 3. 选择OCR引擎进行识别
        var 引擎类型 = 选项.引擎 || OCR_CONFIG.引擎类型;
        var 识别结果;
        
        switch (引擎类型) {
            case "paddle":
                识别结果 = 使用Paddle识别(目标图像, 选项);
                break;
            case "gmlkit":
                识别结果 = 使用GoogleMLKit识别(目标图像, 选项);
                break;
            default:
                识别结果 = 使用Paddle识别(目标图像, 选项);
        }
        
        // 4. 释放图像资源
        屏幕图像.recycle();
        if (目标图像 !== 屏幕图像) {
            目标图像.recycle();
        }
        
        return {
            成功: true,
            识别结果: 识别结果,
            消息: "文字识别完成"
        };
        
    } catch (e) {
        console.error("截图识别失败:", e);
        traceLog("截图识别错误: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "截图识别失败"
        };
    }
}

/**
 * 使用Paddle OCR识别文字
 */
function 使用Paddle识别(图像, 选项) {
    var CPU核心数 = 选项.CPU核心数 || OCR_CONFIG.CPU核心数;
    var 使用快速模型 = 选项.使用快速模型 !== undefined ? 选项.使用快速模型 : OCR_CONFIG.使用快速模型;
    var 置信度阈值 = 选项.置信度阈值 || OCR_CONFIG.置信度阈值;
    
    // 获取完整识别结果
    var 原始结果 = paddle.ocr(图像, CPU核心数, 使用快速模型);
    
    var 文字列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 总置信度 = 0;
    
    if (原始结果 && 原始结果.length > 0) {
        for (var i = 0; i < 原始结果.length; i++) {
            var 项目 = 原始结果[i];
            if (项目.confidence >= 置信度阈值) {
                文字列表.push(项目.text);
                置信度列表.push(项目.confidence);
                坐标列表.push(项目.bounds);
                总置信度 += 项目.confidence;
            }
        }
    }
    
    return {
        引擎: "Paddle OCR",
        识别文字数量: 文字列表.length,
        文字列表: 文字列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        平均置信度: 文字列表.length > 0 ? (总置信度 / 文字列表.length).toFixed(3) : 0,
        完整文本: 文字列表.join(" "),
        原始结果: 原始结果
    };
}

/**
 * 使用Google ML Kit识别文字
 */
function 使用GoogleMLKit识别(图像, 选项) {
    var 语言 = 选项.语言 || OCR_CONFIG.语言;
    
    var 原始结果 = gmlkit.ocr(图像, 语言);
    
    return {
        引擎: "Google ML Kit",
        识别文字数量: 1,
        文字列表: [原始结果.text],
        置信度列表: [1.0], // ML Kit不提供置信度
        坐标列表: [],      // ML Kit不提供详细坐标
        平均置信度: "1.000",
        完整文本: 原始结果.text,
        原始结果: 原始结果
    };
}

/**
 * 快速识别文字（只返回文本）
 */
function 快速识别文字(识别区域) {
    try {
        var 屏幕图像 = images.captureScreen();
        var 目标图像;
        
        if (识别区域 && 识别区域.length === 4) {
            目标图像 = images.clip(屏幕图像, 识别区域[0], 识别区域[1], 识别区域[2], 识别区域[3]);
        } else {
            目标图像 = 屏幕图像;
        }
        
        var 文字数组 = paddle.ocrText(目标图像);
        
        屏幕图像.recycle();
        if (目标图像 !== 屏幕图像) {
            目标图像.recycle();
        }
        
        return 文字数组.join(" ");
        
    } catch (e) {
        console.error("快速识别失败:", e);
        return "";
    }
}

// 导出模块
module.exports = {
    截图识别文字: 截图识别文字,
    快速识别文字: 快速识别文字,
    使用Paddle识别: 使用Paddle识别,
    使用GoogleMLKit识别: 使用GoogleMLKit识别,
    OCR_CONFIG: OCR_CONFIG
};

// 测试代码
if (require.main === module) {
    console.log("🎯 开始测试AutoX.js内置OCR功能...");
    
    // 测试1：全屏Paddle OCR识别
    console.log("\n=== 测试1：Paddle OCR全屏识别 ===");
    var 结果1 = 截图识别文字({
        引擎: "paddle",
        置信度阈值: 0.7
    });
    
    if (结果1.成功) {
        console.log("✅ Paddle OCR识别成功！");
        console.log("📊 识别到 " + 结果1.识别结果.识别文字数量 + " 个文字");
        console.log("📝 完整文本: " + 结果1.识别结果.完整文本);
        console.log("🎯 平均置信度: " + 结果1.识别结果.平均置信度);
        
        // 输出详细结果
        for (var i = 0; i < 结果1.识别结果.文字列表.length; i++) {
            console.log("  文字[" + i + "]: " + 结果1.识别结果.文字列表[i] + 
                       " (置信度: " + 结果1.识别结果.置信度列表[i].toFixed(3) + ")");
        }
    } else {
        console.log("❌ Paddle OCR识别失败: " + 结果1.错误);
    }
    
    // 测试2：Google ML Kit识别
    console.log("\n=== 测试2：Google ML Kit识别 ===");
    var 结果2 = 截图识别文字({
        引擎: "gmlkit",
        语言: "zh"
    });
    
    if (结果2.成功) {
        console.log("✅ Google ML Kit识别成功！");
        console.log("📝 识别文本: " + 结果2.识别结果.完整文本);
    } else {
        console.log("❌ Google ML Kit识别失败: " + 结果2.错误);
    }
    
    // 测试3：区域识别
    console.log("\n=== 测试3：区域识别 ===");
    var 结果3 = 截图识别文字({
        识别区域: [100, 100, 400, 200],  // x, y, width, height
        引擎: "paddle",
        置信度阈值: 0.5
    });
    
    if (结果3.成功) {
        console.log("✅ 区域识别成功！");
        console.log("📝 区域文本: " + 结果3.识别结果.完整文本);
    } else {
        console.log("❌ 区域识别失败: " + 结果3.错误);
    }
    
    // 测试4：快速识别
    console.log("\n=== 测试4：快速识别 ===");
    var 快速结果 = 快速识别文字();
    console.log("⚡ 快速识别结果: " + 快速结果);
    
    console.log("📊 AutoX.js内置OCR功能测试完成");
}