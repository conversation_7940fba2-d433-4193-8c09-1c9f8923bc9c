/**
 * Paddle OCR 符号识别模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：全屏OCR符号识别，专门识别符号，过滤文字
 * 作者：Magic
 * 版本：v4.0
 */

/**
 * 主程序##看广告 - 使用Paddle OCR进行全屏符号识别
 * @param {Object} 选项 - 识别选项（可选）
 * @returns {Object} 识别结果包含符号信息、坐标和置信度
 *
 * 调用方式：
 * 1. 看广告()                           - 使用默认配置全屏符号识别
 * 2. 看广告({置信度阈值: 0.1})           - 自定义置信度阈值
 */
function 看广告(选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(选项);

        // 延迟5秒后再识别
        console.log("⏰ 等待3秒后开始识别...");
        sleep(3000);
        console.log("📸 开始截图识别");

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        traceLog("开始双线程三区域OCR符号识别，配置: " + JSON.stringify(配置));

        var 处理结果;

        // 统一识别符号引擎：原图识别 + 多阈值二值化识别 + 图片匹配识别
        处理结果 = 统一识别符号引擎(屏幕图像, 配置);

        // 释放资源
        屏幕图像.recycle();

        return {
            成功: true,
            识别结果: 处理结果,
            消息: "双线程三区域OCR符号识别完成"
        };

    } catch (e) {
        console.error("看广告OCR识别失败:", e);
        traceLog("OCR识别错误详情: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "OCR识别失败"
        };
    }
}

/**
 * 解析函数参数
 */
function 解析参数(选项) {
    var 默认配置 = {
        CPU核心数: 4,                    // 根据要求改为4
        使用快速模型: false,             // true=快速模型, false=精准模型
        置信度阈值: 0.1,                // 降低置信度阈值，识别更多内容
        启用二值化: true,               // 是否启用二值化处理
        二值化阈值: 186,                // 二值化阈值 (186-255范围)
        二值化最大值: 255,              // 二值化最大值
        反向二值化阈值: 241,            // 反向二值化阈值 (241-255范围)
        反向二值化最大值: 255,          // 反向二值化最大值（大于阈值为0，其他为255）
        启用图片匹配: true,             // 是否启用图片模板匹配
        图片匹配阈值: 0.8,              // 图片匹配相似度阈值 (0-1)
        图片模板路径: "../../assets/算数游戏/广告/右广告",  // 图片模板相对路径
        使用中心点坐标: true            // 是否使用中心点作为主要坐标（true=中心点，false=左上角）
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理选项参数
    if (选项 && typeof 选项 === "object") {
        Object.assign(配置, 选项);
    }

    return 配置;
}

/**
 * 统一图像处理错误处理
 */
function 处理图像错误(错误信息, 处理类型, 原图像, 默认返回值) {
    console.error(处理类型 + "处理失败:", 错误信息);
    console.log("⚠️ 使用原图像进行OCR识别");
    return 默认返回值 || 原图像;
}

/**
 * 多阈值二值化处理引擎 - 单函数多接口，支持双算法多阈值屏幕预处理符号匹配检测
 * @param {Image} 原图像 - 输入的原始图像
 * @param {Object} 配置 - 配置参数
 * @param {string} 处理模式 - "二值化" | "反向二值化" | "全部"
 * @returns {Image|Object} 根据处理模式返回单图像或包含多图像的对象
 */
function 图像二值化处理(原图像, 配置, 处理模式) {
    处理模式 = 处理模式 || "二值化";  // 默认为二值化模式，保持向后兼容

    try {
        // 只进行一次灰度转换（关键优化点）
        var 灰度图像 = images.grayscale(原图像);
        var 结果 = {};

        // 根据处理模式生成对应的图像
        if (处理模式 === "二值化" || 处理模式 === "全部") {
            // 标准二值化处理 (BINARY: 大于阈值为最大值，其他为0)
            结果.二值化图像 = images.threshold(灰度图像, 配置.二值化阈值, 配置.二值化最大值, "BINARY");
        }

        if (处理模式 === "反向二值化" || 处理模式 === "全部") {
            // 反向二值化处理 (BINARY_INV: 大于阈值为0，其他为最大值)
            结果.反向二值化图像1 = images.threshold(灰度图像, 配置.反向二值化阈值, 配置.反向二值化最大值, "BINARY_INV");
        }

        // 释放灰度图像资源
        灰度图像.recycle();

        // 根据处理模式返回相应格式
        if (处理模式 === "二值化") {
            return 结果.二值化图像;  // 向后兼容：返回单图像
        } else if (处理模式 === "反向二值化") {
            return {
                反向二值化图像1: 结果.反向二值化图像1
            };
        } else if (处理模式 === "全部") {
            return {
                二值化图像: 结果.二值化图像,
                反向二值化图像1: 结果.反向二值化图像1
            };
        }

    } catch (e) {
        var 错误类型 = 处理模式 === "二值化" ? "二值化" : "多阈值二值化";
        if (处理模式 === "二值化") {
            return 处理图像错误(e, 错误类型, 原图像);
        } else {
            return 处理图像错误(e, 错误类型, 原图像, {
                反向二值化图像1: 原图像
            });
        }
    }
}

/**
 * 统一识别符号引擎 - 终极优化版本
 * 整合原图识别、多阈值二值化识别、图片匹配识别为一体
 * @param {Image} 屏幕图像 - 输入的屏幕截图
 * @param {Object} 配置 - 配置参数
 * @param {Object} 识别选项 - 可选的识别选项
 * @returns {Object} 统一的识别结果
 */
function 统一识别符号引擎(屏幕图像, 配置, 识别选项) {
    var 开始时间 = Date.now();
    console.log("🚀 启动统一识别符号引擎...");

    // 默认识别选项
    识别选项 = 识别选项 || {
        原图识别: true,
        二值化识别: true,
        反向二值化识别: true,
        图片匹配识别: true
    };

    try {
        // 定义三个识别区域
        var 识别区域列表 = [
            { 名称: "上区域", 坐标: [2, 2, 536, 120] },
            { 名称: "中区域", 坐标: [427, 145, 110, 264] },
            { 名称: "下区域", 坐标: [3, 880, 351, 75] }
        ];

        // 多线程共享变量
        var 线程结果容器 = {
            原图结果: null,
            二值化结果: null,
            反向二值化结果: null,
            图片匹配结果: null
        };
        var 线程完成标志 = {
            线程1完成: false,
            线程2完成: false
        };

        // 第一阶段：多线程并行识别
        var 阶段1开始时间 = Date.now();
        console.log("🚀 第一阶段：启动双线程并行识别...");

        // 线程1：原图识别 + 二值化识别
        var 线程1 = threads.start(function() {
            try {
                console.log("🔥 线程1启动：原图识别 + 二值化识别");

                // 原图识别
                if (识别选项.原图识别) {
                    console.log("   📷 线程1：开始三区域原图OCR识别...");
                    var 原图OCR开始时间 = Date.now();

                    var 区域OCR结果列表 = [];
                    for (var i = 0; i < 识别区域列表.length; i++) {
                        var 区域信息 = 识别区域列表[i];
                        var 区域坐标 = 区域信息.坐标;

                        // 裁剪区域图像
                        var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);

                        // 对区域图像进行OCR识别
                        var 区域OCR结果 = paddle.ocr(区域图像, 配置.CPU核心数, 配置.使用快速模型);

                        // 调整坐标偏移
                        if (区域OCR结果 && 区域OCR结果.length > 0) {
                            for (var j = 0; j < 区域OCR结果.length; j++) {
                                var 项目 = 区域OCR结果[j];
                                项目.bounds.left += 区域坐标[0];
                                项目.bounds.top += 区域坐标[1];
                                项目.bounds.right += 区域坐标[0];
                                项目.bounds.bottom += 区域坐标[1];
                            }
                        }

                        区域OCR结果列表 = 区域OCR结果列表.concat(区域OCR结果 || []);
                        console.log("   ├─ 线程1-" + 区域信息.名称 + " OCR识别完成，识别到: " + (区域OCR结果 ? 区域OCR结果.length : 0) + " 个文本");

                        // 释放区域图像
                        区域图像.recycle();
                    }

                    var 原图OCR耗时 = Date.now() - 原图OCR开始时间;
                    console.log("   ├─ 线程1：三区域原图OCR识别完成，耗时: " + 原图OCR耗时 + "ms，总识别到: " + 区域OCR结果列表.length + " 个文本");

                    线程结果容器.原图结果 = 处理OCR结果(区域OCR结果列表);
                    console.log("   ├─ 线程1：原图结果处理完成，符号数量: " + (线程结果容器.原图结果 ? 线程结果容器.原图结果.符号数量 : 0));
                }

                // 二值化识别
                if (识别选项.二值化识别) {
                    console.log("   🔄 线程1：开始三区域二值化识别...");
                    var 二值化OCR开始时间 = Date.now();

                    var 二值化区域OCR结果列表 = [];
                    for (var k = 0; k < 识别区域列表.length; k++) {
                        var 区域信息 = 识别区域列表[k];
                        var 区域坐标 = 区域信息.坐标;

                        // 裁剪区域图像
                        var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);

                        // 对区域图像进行二值化处理
                        var 区域二值化图像 = 图像二值化处理(区域图像, 配置, "二值化");

                        // 对二值化图像进行OCR识别
                        var 区域二值化OCR结果 = paddle.ocr(区域二值化图像, 配置.CPU核心数, 配置.使用快速模型);

                        // 调整坐标偏移
                        if (区域二值化OCR结果 && 区域二值化OCR结果.length > 0) {
                            for (var l = 0; l < 区域二值化OCR结果.length; l++) {
                                var 项目 = 区域二值化OCR结果[l];
                                项目.bounds.left += 区域坐标[0];
                                项目.bounds.top += 区域坐标[1];
                                项目.bounds.right += 区域坐标[0];
                                项目.bounds.bottom += 区域坐标[1];
                            }
                        }

                        二值化区域OCR结果列表 = 二值化区域OCR结果列表.concat(区域二值化OCR结果 || []);

                        // 释放区域图像
                        区域图像.recycle();
                        区域二值化图像.recycle();
                    }

                    var 二值化OCR耗时 = Date.now() - 二值化OCR开始时间;
                    console.log("   ├─ 线程1：三区域二值化OCR识别完成，耗时: " + 二值化OCR耗时 + "ms，识别到: " + 二值化区域OCR结果列表.length + " 个文本");

                    线程结果容器.二值化结果 = 处理OCR结果(二值化区域OCR结果列表);
                    console.log("   ├─ 线程1：二值化结果处理完成，符号数量: " + (线程结果容器.二值化结果 ? 线程结果容器.二值化结果.符号数量 : 0));
                }

                线程完成标志.线程1完成 = true;
                console.log("✅ 线程1完成所有任务");

            } catch (e) {
                console.error("❌ 线程1执行失败:", e);
                线程完成标志.线程1完成 = true;
            }
        });

        // 线程2：反向二值化识别 + 图片匹配识别
        var 线程2 = threads.start(function() {
            try {
                console.log("🔥 线程2启动：反向二值化识别 + 图片匹配识别");

                // 反向二值化识别
                if (识别选项.反向二值化识别) {
                    console.log("   🔄 线程2：开始三区域反向二值化识别...");
                    var 反向OCR开始时间 = Date.now();

                    var 反向二值化区域OCR结果列表 = [];
                    for (var m = 0; m < 识别区域列表.length; m++) {
                        var 区域信息 = 识别区域列表[m];
                        var 区域坐标 = 区域信息.坐标;

                        // 裁剪区域图像
                        var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);

                        // 对区域图像进行反向二值化处理
                        var 区域反向二值化结果 = 图像二值化处理(区域图像, 配置, "反向二值化");

                        // 对反向二值化图像进行OCR识别
                        var 区域反向二值化OCR结果 = paddle.ocr(区域反向二值化结果.反向二值化图像1, 配置.CPU核心数, 配置.使用快速模型);

                        // 调整坐标偏移
                        if (区域反向二值化OCR结果 && 区域反向二值化OCR结果.length > 0) {
                            for (var n = 0; n < 区域反向二值化OCR结果.length; n++) {
                                var 项目 = 区域反向二值化OCR结果[n];
                                项目.bounds.left += 区域坐标[0];
                                项目.bounds.top += 区域坐标[1];
                                项目.bounds.right += 区域坐标[0];
                                项目.bounds.bottom += 区域坐标[1];
                            }
                        }

                        反向二值化区域OCR结果列表 = 反向二值化区域OCR结果列表.concat(区域反向二值化OCR结果 || []);

                        // 释放区域图像
                        区域图像.recycle();
                        区域反向二值化结果.反向二值化图像1.recycle();
                    }

                    var 反向OCR耗时 = Date.now() - 反向OCR开始时间;
                    console.log("   ├─ 线程2：三区域反向二值化OCR识别完成，耗时: " + 反向OCR耗时 + "ms，识别到: " + 反向二值化区域OCR结果列表.length + " 个文本");

                    线程结果容器.反向二值化结果 = 处理OCR结果(反向二值化区域OCR结果列表);
                    console.log("   ├─ 线程2：反向二值化结果处理完成，符号数量: " + (线程结果容器.反向二值化结果 ? 线程结果容器.反向二值化结果.符号数量 : 0));
                }

                // 图片匹配识别
                if (识别选项.图片匹配识别) {
                    console.log("   🖼️ 线程2：开始三区域图片模板匹配识别...");
                    var 图片匹配开始时间 = Date.now();
                    线程结果容器.图片匹配结果 = 图片模板匹配识别(屏幕图像, 配置);
                    var 图片匹配耗时 = Date.now() - 图片匹配开始时间;
                    console.log("   ├─ 线程2：三区域图片模板匹配完成，耗时: " + 图片匹配耗时 + "ms，符号数量: " + (线程结果容器.图片匹配结果 ? 线程结果容器.图片匹配结果.符号数量 : 0));
                }

                线程完成标志.线程2完成 = true;
                console.log("✅ 线程2完成所有任务");

            } catch (e) {
                console.error("❌ 线程2执行失败:", e);
                线程完成标志.线程2完成 = true;
            }
        });

        // 等待所有线程完成
        console.log("⏳ 等待双线程并行识别完成...");
        while (!线程完成标志.线程1完成 || !线程完成标志.线程2完成) {
            sleep(10);
        }

        var 阶段1耗时 = Date.now() - 阶段1开始时间;
        console.log("✅ 第一阶段（双线程并行识别）完成，总耗时: " + 阶段1耗时 + "ms");



        // 第二阶段：内联智能结果合并（使用多线程结果）
        var 阶段2开始时间 = Date.now();
        console.log("🔗 第二阶段：开始智能结果合并...");

        var 合并符号列表 = [];
        var 合并置信度列表 = [];
        var 合并坐标列表 = [];
        var 合并符号分类 = { 符号: [] };
        var 总置信度 = 0;
        var 有效符号数量 = 0;
        var 坐标容差 = 20; // 用于去重的坐标容差

        // 内联函数：添加符号到合并结果
        function 添加符号到合并结果(符号项, 来源标识) {
            符号项.来源 = 来源标识;
            合并符号分类.符号.push(符号项);
            合并符号列表.push(符号项.符号);
            合并置信度列表.push(符号项.置信度);
            合并坐标列表.push(符号项.坐标);
            总置信度 += 符号项.置信度;
            有效符号数量++;
        }

        // 内联函数：检查并处理重复符号
        function 处理重复符号(新符号项, 来源标识) {
            for (var idx = 0; idx < 合并坐标列表.length; idx++) {
                var 已有坐标 = 合并坐标列表[idx];
                var 距离 = Math.sqrt(
                    Math.pow(新符号项.坐标.centerX - 已有坐标.centerX, 2) +
                    Math.pow(新符号项.坐标.centerY - 已有坐标.centerY, 2)
                );

                if (距离 <= 坐标容差) {
                    // 发现重复，比较置信度
                    if (新符号项.置信度 > 合并符号分类.符号[idx].置信度) {
                        // 新符号置信度更高，替换
                        总置信度 -= 合并符号分类.符号[idx].置信度;
                        总置信度 += 新符号项.置信度;

                        新符号项.来源 = 来源标识 + "(替换)";
                        合并符号分类.符号[idx] = 新符号项;
                        合并符号列表[idx] = 新符号项.符号;
                        合并置信度列表[idx] = 新符号项.置信度;
                        合并坐标列表[idx] = 新符号项.坐标;
                        return "替换"; // 进行了替换
                    } else {
                        return "跳过"; // 发现重复但置信度更低，跳过
                    }
                }
            }
            return false; // 无重复
        }

        // 处理原图结果
        var 原图合并开始时间 = Date.now();
        if (线程结果容器.原图结果 && 线程结果容器.原图结果.符号分类) {
            for (var i = 0; i < 线程结果容器.原图结果.符号分类.符号.length; i++) {
                添加符号到合并结果(线程结果容器.原图结果.符号分类.符号[i], "原图");
            }
            var 原图合并耗时 = Date.now() - 原图合并开始时间;
            console.log("   ├─ 原图结果合并完成，耗时: " + 原图合并耗时 + "ms，添加: " + 线程结果容器.原图结果.符号分类.符号.length + " 个符号");
        } else {
            console.log("   ├─ 原图结果为空，跳过合并");
        }

        // 处理二值化结果（去重）
        var 二值化合并开始时间 = Date.now();
        var 二值化新增数量 = 0;
        var 二值化替换数量 = 0;
        var 二值化跳过数量 = 0;
        if (线程结果容器.二值化结果 && 线程结果容器.二值化结果.符号分类) {
            for (var j = 0; j < 线程结果容器.二值化结果.符号分类.符号.length; j++) {
                var 二值化符号项 = 线程结果容器.二值化结果.符号分类.符号[j];
                var 处理结果 = 处理重复符号(二值化符号项, "二值化");
                if (处理结果 === false) {
                    添加符号到合并结果(二值化符号项, "二值化");
                    二值化新增数量++;
                } else if (处理结果 === "替换") {
                    二值化替换数量++;
                } else if (处理结果 === "跳过") {
                    二值化跳过数量++;
                }
            }
            var 二值化合并耗时 = Date.now() - 二值化合并开始时间;
            console.log("   ├─ 二值化结果合并完成，耗时: " + 二值化合并耗时 + "ms，新增: " + 二值化新增数量 + " 个，替换: " + 二值化替换数量 + " 个，跳过: " + 二值化跳过数量 + " 个");
        } else {
            console.log("   ├─ 二值化结果为空，跳过合并");
        }

        // 处理反向二值化结果（去重）
        var 反向合并开始时间 = Date.now();
        var 反向新增数量 = 0;
        var 反向替换数量 = 0;
        var 反向跳过数量 = 0;
        if (线程结果容器.反向二值化结果 && 线程结果容器.反向二值化结果.符号分类) {
            for (var k = 0; k < 线程结果容器.反向二值化结果.符号分类.符号.length; k++) {
                var 反向符号项 = 线程结果容器.反向二值化结果.符号分类.符号[k];
                var 处理结果 = 处理重复符号(反向符号项, "反向二值化");
                if (处理结果 === false) {
                    添加符号到合并结果(反向符号项, "反向二值化");
                    反向新增数量++;
                } else if (处理结果 === "替换") {
                    反向替换数量++;
                } else if (处理结果 === "跳过") {
                    反向跳过数量++;
                }
            }
            var 反向合并耗时 = Date.now() - 反向合并开始时间;
            console.log("   ├─ 反向二值化结果合并完成，耗时: " + 反向合并耗时 + "ms，新增: " + 反向新增数量 + " 个，替换: " + 反向替换数量 + " 个，跳过: " + 反向跳过数量 + " 个");
        } else {
            console.log("   ├─ 反向二值化结果为空，跳过合并");
        }

        // 处理图片匹配结果（去重）
        var 图片合并开始时间 = Date.now();
        var 图片新增数量 = 0;
        var 图片替换数量 = 0;
        var 图片跳过数量 = 0;
        if (线程结果容器.图片匹配结果 && 线程结果容器.图片匹配结果.符号分类) {
            for (var m = 0; m < 线程结果容器.图片匹配结果.符号分类.符号.length; m++) {
                var 图片符号项 = 线程结果容器.图片匹配结果.符号分类.符号[m];
                var 处理结果 = 处理重复符号(图片符号项, "图片匹配");
                if (处理结果 === false) {
                    添加符号到合并结果(图片符号项, "图片匹配");
                    图片新增数量++;
                } else if (处理结果 === "替换") {
                    图片替换数量++;
                } else if (处理结果 === "跳过") {
                    图片跳过数量++;
                }
            }
            var 图片合并耗时 = Date.now() - 图片合并开始时间;
            console.log("   ├─ 图片匹配结果合并完成，耗时: " + 图片合并耗时 + "ms，新增: " + 图片新增数量 + " 个，替换: " + 图片替换数量 + " 个，跳过: " + 图片跳过数量 + " 个");
        } else {
            console.log("   ├─ 图片匹配结果为空，跳过合并");
        }

        // 构建最终结果
        var 结果构建开始时间 = Date.now();
        var 最终结果 = {
            成功: true,
            符号数量: 有效符号数量,
            符号列表: 合并符号列表,
            置信度列表: 合并置信度列表,
            坐标列表: 合并坐标列表,
            符号分类: 合并符号分类,
            平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
            完整文本: 合并符号列表.join(" "),
            原图结果数量: 线程结果容器.原图结果 ? 线程结果容器.原图结果.符号数量 : 0,
            二值化结果数量: 线程结果容器.二值化结果 ? 线程结果容器.二值化结果.符号数量 : 0,
            反向二值化结果数量: 线程结果容器.反向二值化结果 ? 线程结果容器.反向二值化结果.符号数量 : 0,
            图片匹配结果数量: 线程结果容器.图片匹配结果 ? 线程结果容器.图片匹配结果.符号数量 : 0
        };
        var 结果构建耗时 = Date.now() - 结果构建开始时间;
        console.log("   └─ 最终结果构建完成，耗时: " + 结果构建耗时 + "ms");

        var 阶段2耗时 = Date.now() - 阶段2开始时间;
        console.log("✅ 第二阶段完成，总耗时: " + 阶段2耗时 + "ms");

        var 总耗时 = Date.now() - 开始时间;
        console.log("🎯 ===== 双线程三区域统一识别符号引擎性能分析 =====");
        console.log("   📊 各阶段耗时分布:");
        console.log("   ├─ 第一阶段（双线程并行识别）: " + (typeof 阶段1耗时 !== 'undefined' ? 阶段1耗时 : 0) + "ms");
        console.log("   └─ 第二阶段（结果合并）: " + 阶段2耗时 + "ms");
        console.log("   🏆 总耗时: " + 总耗时 + "ms，最终识别到: " + 最终结果.符号数量 + " 个符号");
        console.log("   📈 平均置信度: " + 最终结果.平均置信度);
        console.log("   📝 识别符号: [" + 最终结果.符号列表.join(", ") + "]");
        console.log("   📍 识别区域: 上区域[2,2,536,120] + 中区域[427,145,110,264] + 下区域[3,880,351,75]");
        console.log("   🧵 并行策略: 线程1(原图+二值化) | 线程2(反向二值化+图片匹配)");
        console.log("⚡ 双线程三区域统一识别符号引擎完成！");

        return 最终结果;

    } catch (e) {
        console.error("❌ 统一识别符号引擎失败:", e);
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: { 符号: [] },
            平均置信度: 0,
            完整文本: "",
            消息: "识别引擎失败: " + e.toString()
        };
    }
}

/**
 * 图片模板匹配识别 - 三区域图片模板匹配
 */
function 图片模板匹配识别(屏幕图像, 配置) {
    try {
        // 定义三个识别区域
        var 识别区域列表 = [
            { 名称: "上区域", 坐标: [2, 2, 536, 120] },
            { 名称: "中区域", 坐标: [427, 145, 110, 264] },
            { 名称: "下区域", 坐标: [3, 880, 351, 75] }
        ];

        // 直接使用相对路径（AutoX.js内置函数支持相对路径）
        var 模板目录路径 = 配置.图片模板路径;

        // 检查目录是否存在
        if (!files.exists(模板目录路径) || !files.isDir(模板目录路径)) {
            console.error("图片模板目录不存在: " + 模板目录路径);
            return {
                成功: false,
                符号数量: 0,
                符号列表: [],
                置信度列表: [],
                坐标列表: [],
                符号分类: { 符号: [] },
                平均置信度: 0,
                完整文本: "",
                错误: "模板目录不存在"
            };
        }

        // 获取目录下所有图片文件（使用相对路径）
        var 图片文件列表 = files.listDir(模板目录路径, function(文件名) {
            // 简化检查：只检查文件扩展名（性能优化）
            var 扩展名 = files.getExtension(文件名).toLowerCase();
            return 扩展名 === "png" || 扩展名 === "jpg" || 扩展名 === "jpeg";
        });

        var 匹配结果列表 = [];
        var 总置信度 = 0;
        var 有效匹配数量 = 0;

        // 遍历每个图片模板进行三区域匹配
        for (var i = 0; i < 图片文件列表.length; i++) {
            var 文件名 = 图片文件列表[i];
            var 模板路径 = 模板目录路径 + "/" + 文件名;

            // 读取模板图片（使用相对路径）
            var 模板图像 = images.read(模板路径);
            if (!模板图像) {
                console.error("无法读取模板图片: " + 模板路径);
                continue;
            }

            // 在三个区域中进行图片匹配
            for (var j = 0; j < 识别区域列表.length; j++) {
                var 区域信息 = 识别区域列表[j];
                var 区域坐标 = 区域信息.坐标;

                // 裁剪区域图像
                var 区域图像 = images.clip(屏幕图像, 区域坐标[0], 区域坐标[1], 区域坐标[2], 区域坐标[3]);

                // 在区域图像中进行图片匹配
                var 匹配结果 = images.findImage(区域图像, 模板图像, {
                    threshold: 配置.图片匹配阈值
                });

                if (匹配结果) {
                    // 获取符号名称（去掉扩展名）
                    var 符号名称 = files.getNameWithoutExtension(文件名);
                    // 使用匹配结果的相似度作为置信度，如果没有则使用阈值
                    var 置信度 = 匹配结果.similarity || 配置.图片匹配阈值;

                    // 计算模板图片的中心点坐标（转换为全屏坐标）
                    var 模板宽度 = 模板图像.getWidth();
                    var 模板高度 = 模板图像.getHeight();
                    var 全屏X = 匹配结果.x + 区域坐标[0];
                    var 全屏Y = 匹配结果.y + 区域坐标[1];
                    var 中心点X = 全屏X + Math.floor(模板宽度 / 2);
                    var 中心点Y = 全屏Y + Math.floor(模板高度 / 2);

                    // 构建匹配结果（使用全屏坐标）
                    var 匹配项 = {
                        符号: 符号名称,
                        置信度: 置信度,
                        坐标: {
                            left: 全屏X,
                            top: 全屏Y,
                            right: 全屏X + 模板宽度,
                            bottom: 全屏Y + 模板高度,
                            centerX: 中心点X,
                            centerY: 中心点Y
                        },
                        来源: "图片匹配(" + 区域信息.名称 + ")"
                    };

                    匹配结果列表.push(匹配项);
                    总置信度 += 置信度;
                    有效匹配数量++;
                }

                // 释放区域图像
                区域图像.recycle();
            }

            // 释放模板图像资源
            模板图像.recycle();
        }

        // 构建返回结果
        var 符号列表 = [];
        var 置信度列表 = [];
        var 坐标列表 = [];

        for (var j = 0; j < 匹配结果列表.length; j++) {
            符号列表.push(匹配结果列表[j].符号);
            置信度列表.push(匹配结果列表[j].置信度);
            坐标列表.push(匹配结果列表[j].坐标);
        }



        return {
            成功: true,
            符号数量: 有效匹配数量,
            符号列表: 符号列表,
            置信度列表: 置信度列表,
            坐标列表: 坐标列表,
            符号分类: { 符号: 匹配结果列表 },
            平均置信度: 有效匹配数量 > 0 ? (总置信度 / 有效匹配数量).toFixed(3) : 0,
            完整文本: 符号列表.join(" ")
        };

    } catch (e) {
        console.error("图片模板匹配失败:", e);
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: { 符号: [] },
            平均置信度: 0,
            完整文本: "",
            错误: e.toString()
        };
    }
}

/**
 * 处理OCR识别结果 - 专门用于符号识别
 */
function 处理OCR结果(OCR结果) {

    if (!OCR结果 || OCR结果.length === 0) {
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: { 符号: [] },
            平均置信度: 0,
            完整文本: "",
            消息: "未识别到符号"
        };
    }



    var 符号列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 符号分类 = { 符号: [] };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];
        var 识别文字 = 项目.text;

        // 符号过滤和分类
        var 符号信息 = 分析符号类型(识别文字);

        if (符号信息.是符号) {
            // 只有符号才计算坐标信息（性能优化）
            var 坐标信息 = {
                left: 项目.bounds.left,
                top: 项目.bounds.top,
                right: 项目.bounds.right,
                bottom: 项目.bounds.bottom,
                centerX: Math.round((项目.bounds.left + 项目.bounds.right) / 2),
                centerY: Math.round((项目.bounds.top + 项目.bounds.bottom) / 2),
                width: 项目.bounds.right - 项目.bounds.left,
                height: 项目.bounds.bottom - 项目.bounds.top
            };

            符号列表.push(识别文字);
            置信度列表.push(项目.confidence);
            坐标列表.push(坐标信息);

            // 符号分类
            var 符号项 = {
                符号: 识别文字,
                类型: 符号信息.类型,
                描述: 符号信息.描述,
                置信度: 项目.confidence,
                坐标: 坐标信息
            };

            // 只保存符号
            符号分类.符号.push(符号项);

            总置信度 += 项目.confidence;
            有效符号数量++;
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 符号列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        符号分类: 符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 符号列表.join(" "),
        原始OCR结果: OCR结果
    };
}

/**
 * 分析符号类型 - 放宽符号识别条件
 */
function 分析符号类型(文字) {
    // 完全禁止识别中文字符

    // 排除任何包含中文字符的内容（优先级最高）
    if (/[\u4e00-\u9fa5]/.test(文字)) {
        return {
            是符号: true,
            类型: "中文",
            描述: "包含中文字符"
        };
    }

    // 检查是否为常见的按钮文字（优先识别为符号）
    var 按钮文字 = ["close", "X"];
    if (按钮文字.indexOf(文字.toLowerCase()) !== -1) {
        return {
            是符号: true,
            类型: "符号",
            描述: "按钮文字符号"
        };
    }

    // 排除包含空格的英文句子
    if (/\s/.test(文字) && /[a-zA-Z]/.test(文字)) {
        return {
            是符号: false,
            类型: "英文句子",
            描述: "包含空格的英文句子"
        };
    }

    // 排除长英文单词（长度>6的纯英文）
    if (/^[a-zA-Z]{7,}$/.test(文字)) {
        return {
            是符号: false,
            类型: "长英文",
            描述: "长英文单词"
        };
    }

    // 排除包含多个英文单词的内容（包含×连接符的长英文）
    if (文字.length > 10 && /[a-zA-Z].*×.*[a-zA-Z]/.test(文字)) {
        return {
            是符号: false,
            类型: "英文句子",
            描述: "包含连接符的英文句子"
        };
    }

    // 识别短内容（长度<=3且不包含中文）
    if (文字.length <= 3) {
        return {
            是符号: true,
            类型: "符号",
            描述: "短符号"
        };
    }

    // 包含数字的内容（不包含中文）
    if (/\d/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含数字的符号"
        };
    }

    // 包含特殊字符的内容（不包含中文）
    if (/[^a-zA-Z0-9\s]/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含特殊字符"
        };
    }

    // 其他视为文字
    return {
        是符号: false,
        类型: "文字",
        描述: "普通文字"
    };
}

/**
 * 符号区域定义 - 管理所有符号的区域配置
 * @returns {Object} 区域配置对象
 */
function 符号区域定义() {
    // 坐标容差范围（±5像素）
    var 坐标容差 = 5;

    return {
        右上角区域: {
            符号列表: ["X", "X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "右双箭头", "右双箭头-1", "右双箭头-2", "播放号", "K", "Y", "X0", ">", "22", "）"],
            中心坐标范围: {
                minX: 469 - 坐标容差, maxX: 528 + 坐标容差,  // 464-533
                minY: 22 - 坐标容差, maxY: 98 + 坐标容差     // 17-103
            },
            优先符号: ["X-1", "X-2", "X-4", "X-5", "X-6", "X-10", "播放号", "右双箭头"],
            排除坐标: []
        },
        右中角区域: {
            符号列表: ["X", "X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11"],
            中心坐标范围: {
                minX: 513 - 坐标容差, maxX: 513 + 坐标容差,  // 508-518
                minY: 311 - 坐标容差, maxY: 311 + 坐标容差   // 306-316
            },
            优先符号: ["X", "X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11"],
            排除坐标: []
        },
        左下角区域: {
            符号列表: ["Close"],
            中心坐标范围: {
                minX: 115 - 坐标容差, maxX: 246 + 坐标容差,  // 110-251
                minY: 820 - 坐标容差, maxY: 912 + 坐标容差   // 815-917
            },
            优先符号: ["Close"],
            排除坐标: []
        },
        顶部区域: {
            符号列表: ["Close"],
            中心坐标范围: {
                minX: 365 - 坐标容差, maxX: 365 + 坐标容差,  // 360-370
                minY: 49 - 坐标容差, maxY: 49 + 坐标容差     // 44-54
            },
            优先符号: ["Close"],
            排除坐标: []
        },
        左上角区域: {
            符号列表: ["X", "X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号", "C", "C-1", "K", "十", "公A"],
            中心坐标范围: {
                minX: 29 - 坐标容差, maxX: 48 + 坐标容差,   // 24-53
                minY: 29 - 坐标容差, maxY: 42 + 坐标容差    // 24-47
            },
            优先符号: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号"],
            排除坐标: []
        },
        横屏右上角区域: {
            符号列表: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号", "X", "K"],
            中心坐标范围: {
                minX: 900 - 坐标容差, maxX: 930 + 坐标容差,  // 895-935
                minY: 26 - 坐标容差, maxY: 48 + 坐标容差     // 21-53
            },
            优先符号: ["X-1", "X-2", "X-3", "X-4", "X-5", "X-6", "X-7", "X-8", "X-9", "X-10", "X-11",
                      "双播放号", "右双箭头", "右双箭头-1", "右双箭头-2", "左播号-1", "播放号", "X", "K"],
            排除坐标: []
        },
        // 全局错误坐标排除
        错误坐标排除: [
            {
                符号: "X-9",
                坐标: {x: 528, y: 906},
                容差范围: {
                    minX: 528 - 10, maxX: 528 + 10,  // 518-538
                    minY: 906 - 10, maxY: 906 + 10   // 896-916
                },
                原因: "与X-10同时出现的异常坐标"
            }
        ]
    };
}

/**
 * 检查符号是否为错误坐标
 * @param {Object} 符号 - 符号对象
 * @param {Object} 区域定义 - 区域配置
 * @returns {boolean} 是否为错误坐标
 */
function 是否为错误坐标(符号, 区域定义) {
    var 错误列表 = 区域定义.错误坐标排除;

    for (var i = 0; i < 错误列表.length; i++) {
        var 错误项 = 错误列表[i];
        if (错误项.符号 === 符号.符号) {
            // 检查坐标是否在错误范围内（±10容差）
            if (符号.坐标.centerX >= 错误项.容差范围.minX && 符号.坐标.centerX <= 错误项.容差范围.maxX &&
                符号.坐标.centerY >= 错误项.容差范围.minY && 符号.坐标.centerY <= 错误项.容差范围.maxY) {
                console.log("⚠️ 排除错误坐标: \"" + 符号.符号 + "\" 坐标(" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ") 容差范围(" + 错误项.容差范围.minX + "-" + 错误项.容差范围.maxX + ", " + 错误项.容差范围.minY + "-" + 错误项.容差范围.maxY + ") 原因: " + 错误项.原因);
                return true;
            }
        }
    }
    return false;
}

/**
 * 检查符号是否为有效点击符号
 * @param {Object} 符号 - 符号对象
 * @param {Object} 区域定义 - 区域配置
 * @returns {boolean} 是否为有效点击符号
 */
function 是否为有效点击符号(符号, 区域定义) {
    // 首先检查是否为错误坐标
    if (是否为错误坐标(符号, 区域定义)) {
        return false;
    }

    // 检查符号是否在任何区域的符号列表中
    var 区域列表 = ["右上角区域", "右中角区域", "左下角区域", "顶部区域", "左上角区域", "横屏右上角区域"];

    for (var i = 0; i < 区域列表.length; i++) {
        var 区域名 = 区域列表[i];
        var 区域配置 = 区域定义[区域名];

        // 检查符号是否在该区域的符号列表中
        if (区域配置.符号列表.indexOf(符号.符号) !== -1) {
            // 检查坐标是否在该区域范围内
            if (符号.坐标.centerX >= 区域配置.中心坐标范围.minX && 符号.坐标.centerX <= 区域配置.中心坐标范围.maxX &&
                符号.坐标.centerY >= 区域配置.中心坐标范围.minY && 符号.坐标.centerY <= 区域配置.中心坐标范围.maxY) {
                return true;
            }
        }
    }

    // 特殊处理：v符号直接点击
    if (符号.符号 === "v") {
        return true;
    }

    return false;
}


/**
 * 执行符号点击 - 合并了符号点击处理逻辑
 * @param {Object} 识别结果或符号对象 - OCR识别结果或单个符号对象
 * @param {string} 点击类型 - 点击类型描述（可选，用于单个符号点击）
 * @returns {boolean} 点击是否成功
 */
function 执行符号点击(识别结果或符号, 点击类型) {
    // 判断传入的是识别结果还是单个符号
    if (识别结果或符号.符号分类) {
        // 传入的是识别结果，执行完整的符号点击处理逻辑
        var 识别结果 = 识别结果或符号;
        var 区域定义 = 符号区域定义();
        var 符号列表 = 识别结果.符号分类.符号;

        // 优先处理图片匹配符号
        var 图片匹配符号 = [];
        for (var i = 0; i < 符号列表.length; i++) {
            var 符号 = 符号列表[i];
            if (符号.来源 === "图片匹配" || 符号.来源 === "图片匹配(替换)") {
                图片匹配符号.push(符号);
            }
        }

        if (图片匹配符号.length > 0) {
            for (var j = 0; j < 图片匹配符号.length; j++) {
                var 图片符号 = 图片匹配符号[j];
                if (是否为有效点击符号(图片符号, 区域定义)) {
                    var 点击结果 = 执行符号点击(图片符号, "图片匹配优先");
                    return 点击结果;
                }
            }
        }

        // 处理其他符号
        for (var k = 0; k < 符号列表.length; k++) {
            var OCR符号 = 符号列表[k];
            if (OCR符号.来源 !== "图片匹配" && OCR符号.来源 !== "图片匹配(替换)") {
                if (是否为有效点击符号(OCR符号, 区域定义)) {
                    var 点击结果2 = 执行符号点击(OCR符号, "OCR识别");
                    return 点击结果2;
                }
            }
        }

        console.log("❌ 未找到可点击的符号");
        return false;

    } else {
        // 传入的是单个符号，执行单个符号点击逻辑
        var 符号 = 识别结果或符号;
        try {
            // 执行点击
            click(符号.坐标.centerX, 符号.坐标.centerY);

            console.log("✅ 符号点击成功: \"" + 符号.符号 + "\" 坐标(" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");

            // 点击后延时（改为2秒）
            sleep(2000);

            return true;
        } catch (e) {
            console.error("❌ 符号点击失败: " + e.toString());
            return false;
        }
    }
}

/**
 * 点击符号后检查游戏状态 - 第八步
 * 检查算数游戏是否在前台，如果不在则切换回来，并检查是否返回游戏开始界面
 * @returns {boolean} 是否成功返回游戏开始界面
 */
function 点击符号后检查游戏状态() {
    console.log("🎯 第八步：检查游戏状态...");

    var 算数游戏 = "com.winrgames.brainbattle";

    // 检查算数游戏是否在前台
    var 当前包名 = currentPackage();
    console.log("🔍 当前应用包名: " + 当前包名);

    if (当前包名 !== 算数游戏) {
        console.log("⚠️ 算数游戏不在前台，正在切换回算数游戏...");

        // 切换回算数游戏
        try {
            app.launch(算数游戏);
            console.log("✅ 已切换回算数游戏应用");
        } catch (e) {
            console.error("❌ 切换回算数游戏失败: " + e.toString());
            return false;
        }
    } else {
        console.log("✅ 算数游戏已在前台");
    }

    // 延时1000毫秒
    sleep(1000);

    // 识别游戏开始界面结束英文图片
    var 游戏开始界面图片路径 = "../../assets/算数游戏/新手教程图片/点击分数.png";
    console.log("🔍 检查是否返回游戏开始界面...");
    console.log("🔍 图片路径: " + 游戏开始界面图片路径);

    try {
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            console.error("❌ 截图失败");
            return false;
        }

        var 模板图像 = images.read(游戏开始界面图片路径);
        if (!模板图像) {
            console.error("❌ 无法读取游戏开始界面图片: " + 游戏开始界面图片路径);
            屏幕图像.recycle();
            return false;
        }

        var 匹配结果 = images.matchTemplate(屏幕图像, 模板图像, {
            threshold: 0.8,
            region: [0, 0, 屏幕图像.getWidth(), 屏幕图像.getHeight()],
            max: 1
        });

        // 释放图像资源
        屏幕图像.recycle();
        模板图像.recycle();

        if (匹配结果.matches.length > 0) {
            console.log("✅ 已返回游戏开始界面，退出看广告循环");
            return true;
        } else {
            console.log("🔍 未检测到游戏开始界面，继续执行");
            return false;
        }

    } catch (e) {
        console.error("❌ 检查游戏开始界面失败: " + e.toString());
        return false;
    }
}

// 看广告精准符号识别功能开始
console.log("\n=== 看广告精准符号识别功能开始 ===");
var 结果 = 看广告();

if (结果.成功) {
    // 输出符号识别结果
    var 分类 = 结果.识别结果.符号分类;
    console.log("\n📋 符号识别结果:");
    console.log("  🔣 识别到符号: " + 分类.符号.length + " 个");


    // 显示所有识别到的符号（精简格式）
    if (分类.符号.length > 0) {
        var 符号简要列表 = [];
        for (var i = 0; i < 分类.符号.length; i++) {
            var 符号 = 分类.符号[i];
            符号简要列表.push(符号.符号 + "(" + 符号.坐标.centerX + "," + 符号.坐标.centerY + ")");
        }
        console.log("🔣 符号详情: " + 符号简要列表.join(" "));

        // 新增：符号点击处理（第七步）
        var 点击结果 = 执行符号点击(结果.识别结果);

        // 新增：点击符号后检查游戏状态（第八步）
        var 游戏状态检查结果 = 点击符号后检查游戏状态();

        if (游戏状态检查结果) {
            console.log("🎯 检测到游戏开始界面，准备退出看广告循环");
            // 这里后续会添加退出循环的逻辑
        }

    } else {
        console.log("  ❌ 未识别到任何符号");

        // 第八步：如果没有识别到符号，也要检查游戏状态
        console.log("🔍 未识别到符号，执行游戏状态检查...");
        var 游戏状态检查结果2 = 点击符号后检查游戏状态();

        if (游戏状态检查结果2) {
            console.log("🎯 检测到游戏开始界面，准备退出看广告循环");
            // 这里后续会添加退出循环的逻辑
        }
    }

} else {
    console.log("❌ 符号识别失败: " + 结果.错误);
}

console.log("📊 算数游戏看广告程序执行完成");