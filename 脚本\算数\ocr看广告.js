/**
 * Paddle OCR 符号识别模块
 * 基于AutoXjs ozobiozobi v6.5.8.17内置OCR功能
 * 适用于雷电模拟器 540x960 DPI 240
 *
 * 功能：全屏OCR符号识别，专门识别符号，过滤文字
 * 作者：Magic项目组
 * 版本：v4.0
 */

/**
 * 看广告 - 使用Paddle OCR进行全屏符号识别
 * @param {Object} 选项 - 识别选项（可选）
 * @returns {Object} 识别结果包含符号信息、坐标和置信度
 *
 * 调用方式：
 * 1. 看广告()                           - 使用默认配置全屏符号识别
 * 2. 看广告({置信度阈值: 0.1})           - 自定义置信度阈值
 */
function 看广告(选项) {
    try {
        // 解析参数
        var 配置 = 解析参数(选项);

        // 延迟5秒后再识别
        console.log("⏰ 等待5秒后开始识别...");
        sleep(5000);
        console.log("📸 开始截图识别");

        // 截图
        var 屏幕图像 = images.captureScreen();
        if (!屏幕图像) {
            throw new Error("截图失败，请检查截图权限");
        }

        traceLog("开始全屏OCR符号识别，配置: " + JSON.stringify(配置));

        // 对屏幕图像进行二值化处理，提高OCR识别精度
        console.log("📸 开始图像二值化处理...");
        var 处理后图像 = 图像二值化处理(屏幕图像, 配置);

        // 使用Paddle OCR进行全屏识别
        var OCR结果 = paddle.ocr(处理后图像, 配置.CPU核心数, 配置.使用快速模型);

        // 处理识别结果
        var 处理结果 = 处理OCR结果(OCR结果, 配置);

        // 释放资源
        屏幕图像.recycle();
        if (处理后图像 !== 屏幕图像) {
            处理后图像.recycle();
        }

        return {
            成功: true,
            识别结果: 处理结果,
            消息: "全屏OCR符号识别完成"
        };

    } catch (e) {
        console.error("看广告OCR识别失败:", e);
        traceLog("OCR识别错误详情: " + e.toString());
        return {
            成功: false,
            错误: e.toString(),
            消息: "OCR识别失败"
        };
    }
}

/**
 * 解析函数参数
 */
function 解析参数(选项) {
    var 默认配置 = {
        CPU核心数: 2,                    // 根据要求改为2
        使用快速模型: false,             // true=快速模型, false=精准模型
        置信度阈值: 0.1,                // 降低置信度阈值，识别更多内容
        启用二值化: true,               // 是否启用二值化处理
        二值化阈值: 186,                // 二值化阈值 (186-255范围)
        二值化最大值: 255               // 二值化最大值
    };

    var 配置 = Object.assign({}, 默认配置);

    // 处理选项参数
    if (选项 && typeof 选项 === "object") {
        Object.assign(配置, 选项);
    }

    return 配置;
}

/**
 * 图像二值化处理 - 提高OCR识别精度
 */
function 图像二值化处理(原图像, 配置) {
    try {
        if (!配置.启用二值化) {
            console.log("📸 跳过二值化处理");
            return 原图像;
        }

        console.log("📸 开始二值化处理，阈值: " + 配置.二值化阈值 + ", 最大值: " + 配置.二值化最大值);

        // 先转换为灰度图像
        var 灰度图像 = images.grayscale(原图像);

        // 进行二值化处理
        var 二值化图像 = images.threshold(灰度图像, 配置.二值化阈值, 配置.二值化最大值, "BINARY");

        // 释放中间图像资源
        灰度图像.recycle();

        console.log("✅ 二值化处理完成");
        return 二值化图像;

    } catch (e) {
        console.error("二值化处理失败:", e);
        console.log("⚠️ 使用原图像进行OCR识别");
        return 原图像;
    }
}

/**
 * 处理OCR识别结果 - 专门用于符号识别
 */
function 处理OCR结果(OCR结果, 配置) {
    console.log("🔍 OCR原始识别结果数量: " + (OCR结果 ? OCR结果.length : 0));

    if (!OCR结果 || OCR结果.length === 0) {
        return {
            成功: false,
            符号数量: 0,
            符号列表: [],
            置信度列表: [],
            坐标列表: [],
            符号分类: { 符号: [] },
            平均置信度: 0,
            完整文本: "",
            消息: "未识别到符号"
        };
    }

    // 调试：显示所有OCR识别结果
    console.log("📋 所有OCR识别内容:");
    for (var j = 0; j < OCR结果.length; j++) {
        var 调试项 = OCR结果[j];
        console.log("  [" + (j + 1) + "] \"" + 调试项.text + "\" (置信度: " + 调试项.confidence.toFixed(3) + ")");
    }

    var 符号列表 = [];
    var 置信度列表 = [];
    var 坐标列表 = [];
    var 符号分类 = { 符号: [] };
    var 总置信度 = 0;
    var 有效符号数量 = 0;

    for (var i = 0; i < OCR结果.length; i++) {
        var 项目 = OCR结果[i];
        var 识别文字 = 项目.text;

        // 符号过滤和分类
        var 符号信息 = 分析符号类型(识别文字);

        console.log("🔍 分析 \"" + 识别文字 + "\": " + (符号信息.是符号 ? "✅符号" : "❌" + 符号信息.类型) + " (置信度: " + 项目.confidence.toFixed(3) + ")");

        if (符号信息.是符号) {
            符号列表.push(识别文字);
            置信度列表.push(项目.confidence);

            // 计算中心坐标
            var 中心坐标 = {
                left: 项目.bounds.left,
                top: 项目.bounds.top,
                right: 项目.bounds.right,
                bottom: 项目.bounds.bottom,
                centerX: Math.round((项目.bounds.left + 项目.bounds.right) / 2),
                centerY: Math.round((项目.bounds.top + 项目.bounds.bottom) / 2),
                width: 项目.bounds.right - 项目.bounds.left,
                height: 项目.bounds.bottom - 项目.bounds.top
            };
            坐标列表.push(中心坐标);

            // 符号分类
            var 符号项 = {
                符号: 识别文字,
                类型: 符号信息.类型,
                描述: 符号信息.描述,
                置信度: 项目.confidence,
                坐标: 中心坐标
            };

            // 只保存符号
            符号分类.符号.push(符号项);

            总置信度 += 项目.confidence;
            有效符号数量++;
        }
    }

    return {
        成功: true,
        符号数量: 有效符号数量,
        符号列表: 符号列表,
        置信度列表: 置信度列表,
        坐标列表: 坐标列表,
        符号分类: 符号分类,
        平均置信度: 有效符号数量 > 0 ? (总置信度 / 有效符号数量).toFixed(3) : 0,
        完整文本: 符号列表.join(" "),
        原始OCR结果: OCR结果
    };
}

/**
 * 分析符号类型 - 放宽符号识别条件
 */
function 分析符号类型(文字) {
    // 完全禁止识别中文字符

    // 排除任何包含中文字符的内容（优先级最高）
    if (/[\u4e00-\u9fa5]/.test(文字)) {
        return {
            是符号: false,
            类型: "中文",
            描述: "包含中文字符"
        };
    }

    // 检查是否为常见的按钮文字（优先识别为符号）
    var 按钮文字 = ["close", "x"];
    if (按钮文字.indexOf(文字.toLowerCase()) !== -1) {
        return {
            是符号: true,
            类型: "符号",
            描述: "按钮文字符号"
        };
    }

    // 排除包含空格的英文句子
    if (/\s/.test(文字) && /[a-zA-Z]/.test(文字)) {
        return {
            是符号: false,
            类型: "英文句子",
            描述: "包含空格的英文句子"
        };
    }

    // 排除长英文单词（长度>6的纯英文）
    if (/^[a-zA-Z]{7,}$/.test(文字)) {
        return {
            是符号: false,
            类型: "长英文",
            描述: "长英文单词"
        };
    }

    // 排除包含多个英文单词的内容（包含×连接符的长英文）
    if (文字.length > 10 && /[a-zA-Z].*×.*[a-zA-Z]/.test(文字)) {
        return {
            是符号: false,
            类型: "英文句子",
            描述: "包含连接符的英文句子"
        };
    }

    // 识别短内容（长度<=3且不包含中文）
    if (文字.length <= 3) {
        return {
            是符号: true,
            类型: "符号",
            描述: "短符号"
        };
    }

    // 识别单个字符（不包含中文）
    if (文字.length === 1) {
        return {
            是符号: true,
            类型: "符号",
            描述: "单个符号"
        };
    }

    // 包含数字的内容（不包含中文）
    if (/\d/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含数字的符号"
        };
    }

    // 包含特殊字符的内容（不包含中文）
    if (/[^a-zA-Z0-9\s]/.test(文字)) {
        return {
            是符号: true,
            类型: "符号",
            描述: "包含特殊字符"
        };
    }

    // 其他视为文字
    return {
        是符号: false,
        类型: "文字",
        描述: "普通文字"
    };
}

// 测试代码 - 直接运行
console.log("🎯 开始测试Paddle OCR精准符号识别功能...");

// 精准符号识别测试
console.log("\n=== 精准符号识别测试 ===");
var 结果 = 看广告();

if (结果.成功) {
    console.log("✅ 符号识别成功！");
    console.log("📊 识别到 " + 结果.识别结果.符号数量 + " 个符号");
    console.log("📝 完整符号: " + 结果.识别结果.完整文本);
    console.log("🎯 平均置信度: " + 结果.识别结果.平均置信度);

    // 输出符号识别结果
    var 分类 = 结果.识别结果.符号分类;
    console.log("\n📋 符号识别结果:");
    console.log("  🔣 识别到符号: " + 分类.符号.length + " 个");

    // 显示所有识别到的符号
    if (分类.符号.length > 0) {
        console.log("\n🔣 符号详情:");
        for (var i = 0; i < 分类.符号.length; i++) {
            var 符号 = 分类.符号[i];
            console.log("  [" + (i + 1) + "] 符号: \"" + 符号.符号 + "\"");
            console.log("      置信度: " + 符号.置信度.toFixed(3));
            console.log("      中心坐标: (" + 符号.坐标.centerX + ", " + 符号.坐标.centerY + ")");
            console.log("      区域: (" + 符号.坐标.left + ", " + 符号.坐标.top + ", " + 符号.坐标.right + ", " + 符号.坐标.bottom + ")");
            console.log("");
        }
    } else {
        console.log("  ❌ 未识别到任何符号");
    }

} else {
    console.log("❌ 符号识别失败: " + 结果.错误);
}

console.log("📊 Paddle OCR精准符号识别功能测试完成");